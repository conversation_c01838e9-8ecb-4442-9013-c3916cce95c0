{"name": "gai-platform-admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 33070", "build": "next build", "start": "next start --port 33070", "lint": "next lint", "prod-eco:pm2": "npm run build && pm2 reload ecosystem.config.cjs", "prod-eco2:pm2": "pm2 reload ecosystem.config.cjs"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@eslint/config-array": "^0.19.0", "@eslint/object-schema": "^2.1.4", "@mui/material": "^5.16.7", "@mui/x-date-pickers": "^7.13.0", "@popperjs/core": "^2.11.8", "@stomp/stompjs": "^7.0.0", "@types/jquery": "^3.5.31", "apexcharts": "^3.54.0", "cookies-next": "^4.2.1", "dayjs": "^1.11.12", "eventemitter3": "^5.0.1", "firebase": "^10.13.1", "glob": "^11.0.0", "jquery.terminal": "^2.44.1", "lru-cache": "^11.0.2", "next": "^14.2.25", "next-auth": "^5.0.0-beta.20", "next-firebase-auth-edge": "^1.7.0-canary.2", "next-fonts": "^1.5.1", "next-themes": "^0.3.0", "raxeraditya-toast": "^1.0.1", "react": "^18", "react-apexcharts": "^1.4.1", "react-cookie": "^7.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-icons": "^5.2.1", "react-json-editor-ajrm": "^2.5.14", "react-quill": "^2.0.0", "react-table": "^7.8.0", "react-use-websocket": "^4.11.1", "rimraf": "^6.0.1", "sockjs-client": "^1.6.1", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-json-editor-ajrm": "^2.5.6", "@types/sockjs-client": "^1.5.4", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-refresh": "^0.4.7", "file-loader": "^6.2.0", "lodash-es": "^4.17.21", "mini-svg-data-uri": "^1.4.4", "postcss": "^8.4.41", "postcss-import": "^16.1.0", "postcss-loader": "^8.1.1", "postcss-nesting": "^12.1.5", "postcss-preset-env": "^9.5.15", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.1", "typescript": "^5", "url-loader": "^4.1.1"}}