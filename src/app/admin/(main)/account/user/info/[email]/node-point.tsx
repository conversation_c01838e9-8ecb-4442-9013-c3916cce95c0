'use client';
import { getAvailablePointSettle, getPointBase, getPointSettleStatus, getPointStatus } from '@/action/point-action';
import { PointStatus, PointStatusResponse } from '@/types/point-status.ts';
import { comma } from '@/utils';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

interface PageProps {
  owner: string;
  locale?: string;
}

class IncomePointAmount {
  totalUsdAmount: number = 0;
  totalKrwAmount: number = 0;
  incomeUsdAmount: number = 0;
  incomeKrwAmount: number = 0;
}

class IncomePoint {
  totalPoint: number = 0;
  incomePoint: number = 0;
}

class TotalSettlePoint {
  taxPoint: number = 0;
  krwSettleAmount: number = 0;
  settlePoint: number = 0;
  usdTaxAmount: number = 0;
  reqPoint: number = 0;
  availablePoint: number = 0;
  krwReqAmount: number = 0;
  usdReqAmount: number = 0;
  usdSettleAmount: number = 0;
  krwTaxAmount: number = 0;
}

class SpendPointAmount {
  availableUsdAmount: number = 0;
  availableKrwAmount: number = 0;
  paymentUsdAmount: number = 0;
  paymentKrwAmount: number = 0;
  spendUsdAmount: number = 0;
  spendKrwAmount: number = 0;
}

class SpendPoint {
  totalSpend: number = 0;
  availablePoint: number = 0;
  paymentPoint: number = 0;
  spendPoint: number = 0;
}
export default function NodeUserPointComponent({ owner, locale = 'ko' }: PageProps) {
  const [pointBase, setPointBase] = useState<any>(null);

  const [userIncomePoint, setUserIncomePoint] = useState<IncomePoint>(new IncomePoint());
  const [userIncomePointAmount, setUserIncomePointAmount] = useState<IncomePointAmount>(new IncomePointAmount());
  const [userTotlaSettlePoint, setUserTotalSettlePoint] = useState<TotalSettlePoint>(new TotalSettlePoint());
  const [availablePoint, setAvailablePoint] = useState<number>(0);

  useEffect(() => {
    // setPointRequest((prevData) => ({ ...prevData, email: email }));

    const init = async () => {
      const pointBaseResponse = await getPointBase();

      if (pointBaseResponse.status == 200) {
        setPointBase(pointBaseResponse.data);
      } else {
        setPointBase(null);
      }
    };

    init();
  }, []);

  useEffect(() => {
    if (pointBase != null) {
      let now = dayjs();
      const startDate = dayjs(`${now.year()}-${now.month() + 1}`).startOf('month');
      const endtDate = dayjs(`${now.year()}-${now.month() + 1}`).endOf('month');

      const pointRequest: any = {
        startTime: startDate.format('YYYY-MM-DD 00:00:00'),
        endTime: endtDate.format('YYYY-MM-DD 23:59:59'),
        owner: owner
      };

      const allPointRequest = { startTime: '', endTime: '', owner: owner };

      //전체
      getPointStatus(allPointRequest)
        .then((response: any) => {
          const points: PointStatus = response.points;

          if (points != undefined && points != null) {
            // 수입
            const totalPoint = points.income.point;

            const totalUsdAmount = points.income.usdAmount;
            const totalKrwAmount = points.income.krwAmount;

            setUserIncomePoint((prevData) => ({ ...prevData, totalPoint: totalPoint }));

            setUserIncomePointAmount((prevData) => ({ ...prevData, totalUsdAmount: totalUsdAmount, totalKrwAmount: totalKrwAmount }));
          }
        })
        .catch((e: any) => {
          console.log(e);
        });

      //기간(이번달)
      getPointStatus(pointRequest)
        .then((response: any) => {
          const points: PointStatus = response.points;

          if (points != undefined && points != null) {
            const incomePoint = points.income.point;

            const incomeUsdAmount = points.income.usdAmount;
            const incomeKrwAmount = points.income.krwAmount;

            setUserIncomePoint((prevData) => ({ ...prevData, incomePoint: incomePoint }));

            setUserIncomePointAmount((prevData) => ({ ...prevData, incomeUsdAmount: incomeUsdAmount, incomeKrwAmount: incomeKrwAmount }));
          }
        })
        .catch((e: any) => {
          console.log(e);
        });

      //사용자 전체 포인트 정산
      getPointSettleStatus(owner)
        .then((response: any) => {
          console.log(response.point);
          const settle: TotalSettlePoint = response.points.settle;
          console.log(settle);
          if (settle != undefined && settle != null) {
            // const availableSettlePoint = settle.availableSettlePoint;
            // const totalIncomePoint = settle.totalIncomePoint;
            // const reqPoint = settle.reqPoint;
            // const totalSettlePoint = settle.totalSettlePoint;

            setUserTotalSettlePoint(settle);
          }
        })
        .catch((e: any) => {
          console.log(e);
        });

      // @brief 사용자 정산 가능 포인트
      getAvailablePointSettle(owner)
        .then((response: any) => {
          console.log(response);
          if (response.status === 200) {
            setAvailablePoint(response.availablePoint);
          } else {
            setAvailablePoint(0);
          }
          // const settle: TotalSettlePoint = response.settle;
        })
        .catch((e: any) => {
          console.log(e);
        });
    }
  }, [pointBase]);

  return (
    <>
      <div className="flex flex-col gap-7.5">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card min-w-full">
            <div className="card-header">
              <h3 className="card-title">수입 포인트 정보</h3>
            </div>
            <div className="card-body lg:py-7.5">
              <div className="flex flex-col gap-3">
                <div className="grid grid-cols-1 lg:grid-cols-4">
                  <div className="flex flex-col items-center py-1.5 lg:gap-4 lg:px-10">
                    <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                      <span className="text-[14px] font-medium leading-[14px] text-gray-600">이번달 포인트</span>
                      <span className="text-[30px] font-medium leading-[30px] text-gray-900">
                        {comma(userIncomePoint.incomePoint ?? 0) + 'P'}
                      </span>
                      <span className="text-[20px] font-normal leading-[30px] text-gray-500"></span>
                    </div>
                  </div>

                  <div className="flex flex-col items-center py-1.5 lg:gap-4 lg:px-10">
                    <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                      <span className="text-[14px] font-medium leading-[14px] text-gray-600">전체 수입 포인트</span>
                      <span className="text-[30px] font-medium leading-[30px] text-gray-900">
                        {comma(userIncomePoint.totalPoint ?? 0) + 'P'}
                      </span>
                      <span className="text-[20px] font-normal leading-[30px] text-gray-500"></span>
                    </div>
                  </div>

                  <div className="flex flex-col items-center py-1.5 lg:gap-4 lg:px-10">
                    <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                      <span className="text-[14px] font-medium leading-[14px] text-gray-600">전체 정산 포인트</span>
                      <span className="text-[30px] font-medium leading-[30px] text-gray-900">
                        {comma(userTotlaSettlePoint.reqPoint ?? 0) + 'P'}
                      </span>
                      <span className="text-[20px] font-normal leading-[30px] text-gray-500"></span>
                    </div>
                  </div>

                  <div className="flex flex-col items-center py-1.5 lg:gap-4 lg:px-10">
                    <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                      <span className="text-[14px] font-medium leading-[14px] text-gray-600">출금 가능 포인트</span>
                      <span className="text-[30px] font-medium leading-[30px] text-gray-900">
                        {comma(userTotlaSettlePoint.availablePoint ?? 0) + 'P'}
                      </span>
                      <span className="text-[20px] font-normal leading-[30px] text-gray-500"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
