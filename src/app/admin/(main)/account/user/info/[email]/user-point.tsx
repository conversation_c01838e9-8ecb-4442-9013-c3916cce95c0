'use client';

import { useState } from 'react';
import WorkloadPointComponent from './workload-point';
import NodePointComponent from './node-point';
import WorkloadPointStatusComponent from './workload-point-status';
import WorkloadPointChargeComponent from './workload-point-charge';
import WorkloadPointPaymentComponent from './workload-point-payment';
import WorkloadPointRefundComponent from './workload-point-refund';
import NodePointStatusComponent from './node-point-status';
import NodeSettlePointComponent from './node-settle-point';

interface PageProps {
  owner: string;
}
export default function UserPointPage({ owner }: PageProps) {
  const [pointType, setPointType] = useState<string>('workload');
  return (
    <>
      <div className="container-fixed my-5">
        <div className="flex flex-col border-b border-b-gray-200 pt-5">
          <div className="flex flex-wrap items-center justify-between gap-5 pb-7.5 lg:items-end">
            <div className="flex flex-col justify-center gap-2">
              <h1 className="text-xl font-semibold leading-none text-gray-900">포인트 현황</h1>
              {/* <div className="flex items-center gap-2 text-sm font-medium text-gray-600">Central Hub for Personal Customization</div> */}
            </div>
          </div>

          <div className="flex flex-col gap-5">
            <div className="flex w-full flex-nowrap gap-5">
              <div className="grid">
                <div className="scrollable-x-auto">
                  <div className="menu gap-3" data-menu="true">
                    <div
                      className={`menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary ${pointType === 'workload' ? 'active' : ''}`}
                    >
                      <button
                        className="menu-link gap-1.5 px-2 pb-2 lg:pb-4"
                        tabIndex={0}
                        onClick={() => {
                          setPointType('workload');
                        }}
                      >
                        <span className="menu-title text-nowrap text-sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-item-here:font-semibold menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary">
                          워크로드
                        </span>
                      </button>
                    </div>
                    <div
                      className={`menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary ${pointType === 'node' ? 'active' : ''}`}
                    >
                      <button
                        className="menu-link gap-1.5 px-2 pb-2 lg:pb-4"
                        tabIndex={0}
                        onClick={() => {
                          setPointType('node');
                        }}
                      >
                        <span className="menu-title text-nowrap text-sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-item-here:font-semibold menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary">
                          노드
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {pointType === 'workload' && (
        <>
          <div className="container-fixed pt-5">
            <WorkloadPointComponent owner={owner}></WorkloadPointComponent>
          </div>
          <div className="container-fixed pt-5">
            <WorkloadPointChargeComponent owner={owner}></WorkloadPointChargeComponent>
          </div>
          <div className="container-fixed pt-5">
            <WorkloadPointRefundComponent owner={owner}></WorkloadPointRefundComponent>
          </div>
          <div className="container-fixed pt-5">
            <WorkloadPointPaymentComponent owner={owner}></WorkloadPointPaymentComponent>
          </div>
          <div className="container-fixed pt-5">
            <WorkloadPointStatusComponent owner={owner}></WorkloadPointStatusComponent>
          </div>
        </>
      )}
      {pointType === 'node' && (
        <>
          <div className="container-fixed pt-5">
            <NodePointComponent owner={owner}></NodePointComponent>
          </div>
          <div className="container-fixed pt-5">
            <NodeSettlePointComponent owner={owner}></NodeSettlePointComponent>
          </div>
          <div className="container-fixed pt-5">
            <NodePointStatusComponent owner={owner}></NodePointStatusComponent>
          </div>
        </>
      )}
    </>
  );
}
