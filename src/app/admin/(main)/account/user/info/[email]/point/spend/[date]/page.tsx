import Link from 'next/link';

import { IoIosArrowBack } from 'react-icons/io';

import UserPointSpendHistoryPage from './user-point-spend-history';

interface PageProps {
  params: {
    email: string;
    date: string;
  };
}
export default async function PointHistory({ params }: PageProps) {
  const date = params.date;
  const email = params.email;
  return (
    <>
      <div className="container-fixed gap-5 pb-[60px]">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
          <div className="flex flex-nowrap items-center justify-center gap-2">
            <Link href={`/admin/account/user/info/${email}`} className="flex flex-nowrap items-center hover:text-primary">
              <IoIosArrowBack size={'1.3em'} />
            </Link>
            <h1 className="pl-1 text-xl font-semibold leading-none text-gray-900">{date} 포인트 내역</h1>
          </div>
          {/* <div className="flex items-center gap-2.5">{workload.description}</div> */}
        </div>
        <div className="grid gap-8">
          <UserPointSpendHistoryPage date={date} email={email}></UserPointSpendHistoryPage>
        </div>
      </div>
    </>
  );
}
