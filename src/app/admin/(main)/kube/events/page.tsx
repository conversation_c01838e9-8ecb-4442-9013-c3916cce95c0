import { getPodEvents } from '@/action/pod-action';
import { PodEvent, PodEventResponse, PodEventRequest } from '@/types/pod';
import { Paging } from '@/types/paging';
import EventsTable from './events-table';

type PageProps = {
  searchParams: { [key: string]: string | string[] | undefined };
};

export default async function EventListPage({ searchParams }: PageProps) {
  let eventRequest: PodEventRequest = {
    startNum: 0,
    scaleNum: 30,
    type: '',
    owner: '',
    podName: '',
    nodeName: '',
    startTime: '',
    endTime: ''
  };
  let eventList: PodEvent[] = [];
  let paging: Paging | null = null;

  const search = async () => {
    for (const key in searchParams) {
      if (searchParams[key] !== undefined && key in eventRequest) {
        const value = searchParams[key];
        if (key === 'startNum' || key === 'scaleNum') {
          eventRequest[key] = Number(value);
        } else if (key === 'type' || key === 'owner' || key === 'podName'  || key === 'nodeName' || key === 'startTime' || key === 'endTime') {
          eventRequest[key] = value as string;
        }
      }
    }
    const eventResponse: PodEventResponse = await getPodEvents(eventRequest);
    if (eventResponse.status == 200) {
      eventList = eventResponse.events;
      paging = eventResponse.paging as Paging;
    }
  };

  await search();

  return (
    <>
      <div className="container-fixed">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-7.5 lg:items-end">
          <div className="flex flex-col justify-center gap-2">
            <h1 className="text-xl font-semibold leading-none text-gray-900">배포 이벤트</h1>
          </div>
        </div>
        <EventsTable eventList={eventList} paging={paging} eventRequest={eventRequest}></EventsTable>
      </div>
    </>
  );
}
