'use client';
import Paginate from '@/components/paging/paginate';
import { Paging } from '@/types/paging';
import { PodEvent, PodEventRequest } from '@/types/pod';
import { generateUrlSearchParams, isBlank, dateTime, dateTime2 } from '@/utils';
import { usePathname } from 'next/navigation';
import React, { ChangeEvent, KeyboardEvent, MouseEvent, useCallback, useEffect, useRef, useState } from 'react';
import { FaMinus, FaPlus } from 'react-icons/fa6';
import Link from 'next/link';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { User, UserResponse } from '@/types/user';
import { KTDropdown } from '@/metronic/core';
import { getSearchUser } from '@/action/user-action';
import { debounce } from 'lodash-es';
import { getSearchPodName } from '@/action/pod-action';
import { getSearchNode } from '@/action/node-action';

interface PageProps {
  eventList: PodEvent[];
  paging: Paging;
  eventRequest: PodEventRequest;
}
export default function EventsTable({ eventList, paging, eventRequest }: PageProps) {
  const pathName = usePathname();
  const [eventsData, setEventsData] = useState<PodEvent[]>([]);
  const [pagingData, setPageData] = useState<Paging | null>(null);
  const [extendRow, setExtendRow] = useState<boolean[]>([]);
  const [eventRequestData, setEventRequestData] = useState<PodEventRequest>({
    startNum: 0,
    scaleNum: 15,
    type: '',
    owner: '',
    podName: '',
    nodeName: '',
    startTime: '',
    endTime: ''
  });

  const ownerDropDownRef = useRef<any>(null);
  const podNameDropDownRef = useRef<any>(null);
  const nodeNameDropDownRef = useRef<any>(null);
  const [ownerSearchList, setOwnerSearchList] = useState<User[]>([]);
  const [podNameSearchList, setPodNameSearchList] = useState<string[]>([]);
  const [nodeNameSearchList, setNodeNameSearchList] = useState<string[]>([]);

  useEffect(() => {
    const init = async () => {
      setEventsData(eventList);
      setPageData(paging);
      setEventRequestData(eventRequest);
    };

    const initDropdown = () => {
      const ownerDropdownEl = document.querySelector('#owner_dropdown') as HTMLElement;
      ownerDropDownRef.current = KTDropdown.getInstance(ownerDropdownEl);

      if (eventRequest?.owner) {
        searchIntelliSense('owner', eventRequest?.owner, false);
      }
    };

    init();
    initDropdown();
  }, []);

  /**
   * @brief INPUT 값 변경
   * @param e
   */
  const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setEventRequestData((prevData) => ({ ...prevData, [name]: value }));

    //사용자 이메일/이름 검색 후 자동완성 기능
    debounceSearchIntelliSense(name, value);
  };

  const onSelectChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setEventRequestData((prevData) => ({ ...prevData, [name]: value }));
  };
  const changeDataTime = (name: string, value: Dayjs) => {
    setEventRequestData((prevData) => ({ ...prevData, [name]: dayjs(value).format('YYYY-MM-DD') }));
  };
  const onSearch = async (startNum: number) => {
    setEventRequestData((prevData) => ({ ...prevData, startNum: startNum }));
    const requestData = eventRequestData;
    requestData.startNum = startNum;
    const queryStr = generateUrlSearchParams(requestData);
    window.location.href = `${pathName}?${queryStr}`;
  };

  /**
   * @brief 응답유형 변환
   * @param item
   * @returns
   */
  const convertResType = (item: string) => {
    if (item == 'ADDED') {
      return '등록';
    } else if (item == 'MODIFIED') {
      return '수장';
    } else if (item == 'DELETED') {
      return '삭제';
    } else {
      return '';
    }
  };
  /**
   * @brief 이벤트 유형 변환
   * @param item
   * @returns
   */
  const convertType = (item: string) => {
    if (item == 'Normal') {
      return <span>일반</span>;
    } else if (item == 'Warning') {
      return <span className="text-warning">경고</span>;
    } else {
      return '';
    }
  };

  const ser = (podName: string) => {
    return podName.split('-')[0].replace('dep', '');
  };

  /**
   * @brief Input 인텔리센스 지연 실행
   */
  const debounceSearchIntelliSense = useCallback(
    debounce(
      (name: string, value: string) => {
        searchIntelliSense(name, value);
      },
      200,
      { leading: false, trailing: true }
    ),
    []
  );

  /**
   * @brief Input 인텔리센스
   * @param name
   * @param value
   */
  const searchIntelliSense = (name: string, value: string, display: boolean = true) => {
    console.log('이벤트 ', name, value);
    const type = name === 'owner' ? 'email' : name;
    if (name === 'owner') {
      getSearchUser(type, value)
        .then((response: UserResponse) => {
          if (response.status === 200) {
            if (name === 'owner') {
              setOwnerSearchList(response.users);
              display ? ownerDropDownRef?.current.show() : ownerDropDownRef?.current.hide();
            }
          } else {
            if (name === 'owner') {
              setOwnerSearchList([]);
              display ? ownerDropDownRef?.current.show() : ownerDropDownRef?.current.hide();
            }
          }
        })
        .catch((err: any) => {
          console.log(err);
        });
    } else if (name === 'podName') {
      getSearchPodName(value)
        .then((response: any) => {
          if (response.status === 200) {
            if (name === 'podName') {
              setPodNameSearchList((response.users as string[]) || (response.pods as string[]));
              display ? podNameDropDownRef?.current.show() : podNameDropDownRef?.current.hide();
            }
          } else {
            if (name === 'podName') {
              setPodNameSearchList([]);
              display ? podNameDropDownRef?.current.show() : podNameDropDownRef?.current.hide();
            }
          }
        })
        .catch((err: any) => {
          console.log(err);
        });
    } else if (name === 'nodeName') {
      getSearchNode(value)
        .then((response: any) => {
          if (response.status === 200) {
            if (name === 'nodeName') {
              setNodeNameSearchList(response.nodes as string[]);
              display ? nodeNameDropDownRef?.current.show() : nodeNameDropDownRef?.current.hide();
            }
          } else {
            if (name === 'nodeName') {
              setNodeNameSearchList([]);
              display ? nodeNameDropDownRef?.current.show() : nodeNameDropDownRef?.current.hide();
            }
          }
        })
        .catch((err: any) => {
          console.log(err);
        });
    }
  };

  /**
   * @brief 사용자 선택
   * @param name
   * @param value
   */
  const selectUser = (name: string, value: string) => {
    setEventRequestData((prevData) => ({ ...prevData, [name]: value }));
    if (name === 'owner') {
      const target = ownerSearchList.filter((item) => item.email === value);
      setOwnerSearchList(target);
      ownerDropDownRef?.current?.hide();
    } else if (name === 'podName') {
      const target = podNameSearchList.filter((item) => item === value);
      setPodNameSearchList(target);
      podNameDropDownRef?.current?.hide();
    } else if (name === 'nodeName') {
      const target = nodeNameSearchList.filter((item) => item === value);
      setNodeNameSearchList(target);
      nodeNameDropDownRef?.current?.hide();
    }
  };

  const onInputEnter = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSearch(0);
    }
  };

  // @ts-ignore
  return (
    <>
      <div className="card min-w-full">
        <div className="card-header flex-wrap gap-5 py-5">
          <div className="flex gap-6">
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="switch switch-sm">
                <button className="btn btn-sm btn-light" onClick={() => onSearch(eventRequestData.startNum)}>
                  <i className="ki-filled ki-arrows-circle"></i>
                </button>
              </label>
              <label className="form-label w-16">수요자</label>
              <div className="dropdown" data-dropdown="true" id="owner_dropdown" data-dropdown-trigger="click" data-dropdown-dismiss="true">
                <div className="dropdown-toggle flex grow flex-col items-start gap-5">
                  <div className="relative w-full">
                    <input
                      className="input input-sm"
                      name="owner"
                      placeholder="E-Mail, 네임스페이스"
                      type="text"
                      value={eventRequestData?.owner}
                      onChange={onInputChange}
                      onKeyDown={onInputEnter}
                    />
                  </div>
                </div>
                <div className="dropdown-content w-full max-w-[500px] p-4">
                  <div className="menu menu-default scrollable-y flex max-h-[400px] w-full flex-col">
                    {ownerSearchList.length > 0 ? (
                      <>
                        <div className="menu-item" key={`owner_search_key_a`}>
                          <span className="menu-title">검색 : {ownerSearchList.length} 개</span>
                        </div>

                        <div className="menu-separator"></div>

                        {ownerSearchList.map((item, index) => (
                          <div className="menu-item" key={`owner_search_key_${index}`}>
                            <div className="grid grid-cols-2">
                              <button className="menu-link" onClick={() => selectUser('owner', item.email)}>
                                <span className="menu-icon">
                                  <i className="ki-outline ki-badge"></i>
                                </span>
                                <span className="menu-title">{item.email}</span>
                              </button>
                              <button className="menu-link" onClick={() => selectUser('namespace', item.namespace)}>
                                <span className="menu-icon">
                                  <i className="ki-outline ki-badge"></i>
                                </span>
                                <span className="menu-title">{item.namespace}</span>
                              </button>
                            </div>
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="menu-item" key={`owner_search_key_a`}>
                        {eventRequestData?.owner.length > 0 ? (
                          <span className="menu-title">검색된 이메일/네이스페이스가 없습니다.</span>
                        ) : (
                          <span className="menu-title">이메일/네이스페이스를를 입력하세요.</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">파드이름</label>
              <div className="dropdown" data-dropdown="true" id="owner_dropdown" data-dropdown-trigger="click" data-dropdown-dismiss="true">
                <div className="dropdown-toggle flex grow flex-col items-start gap-5">
                  <div className="relative w-full">
                    <input
                      className="input input-sm"
                      name="podName"
                      placeholder="파드이름"
                      type="text"
                      value={eventRequestData?.podName}
                      onChange={onInputChange}
                      onKeyDown={onInputEnter}
                    />
                  </div>
                </div>
                <div className="dropdown-content w-full max-w-[320px] p-4">
                  <div className="menu menu-default scrollable-y flex max-h-[400px] w-full flex-col">
                    {podNameSearchList.length > 0 ? (
                      <>
                        <div className="menu-item" key={`podName_search_key_a`}>
                          <span className="menu-title">검색 : {podNameSearchList.length} 개</span>
                        </div>

                        <div className="menu-separator"></div>

                        {podNameSearchList.map((item, index) => (
                          <div className="menu-item" key={`podName_search_key_${index}`}>
                            <div className="grid">
                              <button className="menu-link" onClick={() => selectUser('podName', item)}>
                                <span className="menu-icon">
                                  <i className="ki-outline ki-badge"></i>
                                </span>
                                <span className="menu-title">{item}</span>
                              </button>
                            </div>
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="menu-item" key={`podName_search_key_a`}>
                        {eventRequestData?.podName.length > 0 ? (
                          <span className="menu-title">검색된 파드이름이 없습니다.</span>
                        ) : (
                          <span className="menu-title">파드이름을 입력하세요.</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">노드이름</label>
              <div className="dropdown" data-dropdown="true" id="node_dropdown" data-dropdown-trigger="click" data-dropdown-dismiss="true">
                <div className="dropdown-toggle flex grow flex-col items-start gap-5">
                  <div className="relative w-full">
                    <input
                      className="input input-sm"
                      name="nodeName"
                      placeholder="wn"
                      type="text"
                      value={eventRequestData?.nodeName}
                      onChange={onInputChange}
                      onKeyDown={onInputEnter}
                    />
                  </div>
                </div>
                <div className="dropdown-content w-full max-w-[400px] p-4">
                  <div className="menu menu-default scrollable-y flex max-h-[400px] w-full flex-col">
                    {nodeNameSearchList.length > 0 ? (
                      <>
                        <div className="menu-item" key={`node_search_key_a`}>
                          <span className="menu-title">검색 : {nodeNameSearchList.length} 개</span>
                        </div>

                        <div className="menu-separator"></div>

                        {nodeNameSearchList.map((item, index) => (
                          <div className="menu-item" key={`node_search_key_${index}`}>
                            <button className="menu-link" onClick={() => selectUser('nodeName', item)}>
                              <span className="menu-icon">
                                <i className="ki-outline ki-badge"></i>
                              </span>
                              <span className="menu-title">{item}</span>
                            </button>
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="menu-item" key={`node_search_key_a`}>
                        {eventRequestData?.nodeName.length > 0 ? (
                          <span className="menu-title">검색된 노드가 없습니다.</span>
                        ) : (
                          <span className="menu-title">노드이름을 입력하세요.</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">유형</label>
              <div className="flex grow flex-col items-start gap-5">
                <div className="relative w-32">
                  <select className="select w-28" name="category" value={eventRequestData?.type} onChange={onSelectChange}>
                    <option value="">선택</option>
                    <option value="Normal">일반</option>
                    <option value="Waring">경고</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-6">
            <div className="flex flex-wrap items-center gap-2.5"></div>
            <div className="flex flex-wrap items-center gap-2.5">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <label className="form-label w-16">발생일시</label>
                <div className="flex flex-col flex-wrap gap-2.5">
                  <DatePicker
                    name="startTime"
                    onChange={(value) => changeDataTime('startTime', value)}
                    value={isBlank(eventRequestData?.startTime) ? null : dayjs(eventRequestData?.startTime)}
                    className="input-sm max-w-40 !pl-0 !pr-0"
                    format="YYYY-MM-DD"
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </div>
                <div className="flex hidden flex-wrap items-center gap-2.5 lg:block lg:flex-col lg:justify-center"> -</div>
                <div className="flex flex-col flex-wrap gap-2.5">
                  <DatePicker
                    name="endTime"
                    onChange={(value) => changeDataTime('endTime', value)}
                    value={isBlank(eventRequestData?.endTime) ? null : dayjs(eventRequestData?.endTime)}
                    className="input-sm max-w-40 !pl-0 !pr-0"
                    format="YYYY-MM-DD"
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </div>
              </LocalizationProvider>
            </div>
            <label className="switch switch-sm">
              <button className="btn btn-sm btn-light" onClick={() => onSearch(0)}>
                검색
              </button>
            </label>
          </div>
        </div>
        <div className="card-table scrollable-x-auto">
          <div className="scrollable-auto">
            <table className="d-node-table table align-middle text-2xs text-gray-600">
              <thead>
                <tr className="text-nowrap text-left">
                  <th className="text-left !font-semibold">응답 유형</th>
                  <th className="!font-semibold">이벤트 유형</th>
                  <th className="!font-semibold">이벤트 이유</th>
                  <th className="!font-semibold">보고 구성 요소</th>
                  <th className="!font-semibold">이벤트 대상 유형</th>
                  <th className="!font-semibold">발생횟수</th>
                  <th className="!font-semibold">파드</th>
                  <th className="!font-semibold">노드</th>
                  <th className="!font-semibold">네임스페이스</th>
                  <th className="!font-semibold">최초발생일시</th>
                  <th className="!font-semibold">최근발생일시</th>
                  <th className="text-center !font-semibold">상세보기</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {eventsData.length > 0 &&
                  eventsData.map((item, index) => (
                    <React.Fragment key={`event_${index}`}>
                      <tr key={`pod_event_${index}`} className="text-nowrap text-left">
                        <td className="text-left">{convertResType(item.resType)}</td>
                        <td className="text-left">{convertType(item.type)}</td>
                        <td>{item.reason}</td>
                        <td>{item.reportingComponent}</td>
                        <td>{item.kind}</td>
                        <td>{item.count}</td>
                        <td>
                          <Link
                            className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active"
                            href={`/admin/workload/detail/${ser(item.name)}`}
                          >
                            {item.name}
                          </Link>
                        </td>
                        <td>
                          {item.nodeName.length > 0 && (
                            <Link
                              className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active"
                              href={`/admin/node/detail/${item.nodeName}`}
                            >
                              {item.nodeName}
                            </Link>
                          )}
                        </td>
                        <td>
                          <Link
                            className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active"
                            href={`/admin/account/user/info/${item.namespace}`}
                          >
                            {item.namespace}
                          </Link>
                        </td>
                        <td>{dateTime2(item.firstTimestamp)}</td>
                        <td>{dateTime2(item.lastTimestamp)}</td>
                        <td className="text-center">
                          <button
                            className="btn-icon btn btn-sm btn-clear btn-light"
                            data-index={index}
                            onClick={(e: MouseEvent<HTMLButtonElement>) => {
                              const idx = Number(e.currentTarget.getAttribute('data-index'));
                              setExtendRow((prevData) => {
                                const updateForm = [...prevData];
                                updateForm[idx] = !prevData[idx];
                                return updateForm;
                              });
                            }}
                          >
                            {extendRow[index] ? <FaMinus /> : <FaPlus />}
                          </button>
                        </td>
                      </tr>
                      <tr key={`pod_event_message_${index}`} className={`${extendRow[index] ? 'show' : 'hidden'}`}>
                        <td colSpan={12} className="bg-gray-100">
                          <div className="py-5 text-center">
                            <pre className="text-wrap text-balance break-all">{item.message}</pre>
                          </div>
                        </td>
                      </tr>
                    </React.Fragment>
                  ))}
                {eventsData.length == 0 && (
                  <tr>
                    <td colSpan={11} className="text-center">
                      <div className="flex flex-col items-center p-10">
                        <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                        조회된 배포 이벤트가 없습니다.
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
        <div className="card-footer flex-col justify-center gap-5 text-2sm font-medium text-gray-600 md:flex-row md:justify-between">
          <Paginate data={paging} requestData={eventRequestData}></Paginate>
        </div>
      </div>
    </>
  );
}
