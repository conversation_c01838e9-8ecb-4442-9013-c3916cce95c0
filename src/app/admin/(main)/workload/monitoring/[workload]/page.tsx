import { UsageRequest, UsageResponse } from '@/types/usage';
import { Workload, WorkloadPod } from '@/types/workload';
import { getWorkload } from '@/action/workload-action';
import { getPodsByWorkload } from '@/action/pod-action';
import WorkloadMonitoringHeader from './monitoring-header';
import WorkloadMonitoringPanel from './monitoring-panel';
import { generateNamespace } from '@/utils/gai-utils';
import dayjs from 'dayjs';
import Link from 'next/link';
import { IoIosArrowBack } from 'react-icons/io';

interface PageProps {
  params: {
    workload: string;
  };
}

/**
 * @brief Workload 모니터링 페이지
 * @param params
 * @returns
 */
export default async function WorkloadMonitoringPage({ params }: PageProps) {
  let d = new Date();
  const startDate = dayjs(d).subtract(8, 'hour');
  const endtDate = dayjs(d);

  const usageRequest: UsageRequest = {
    owner: '',
    namespace: '',
    node: '',
    pod: '',
    interval: 600,
    startTime: startDate.format('YYYY-MM-DD HH:mm:00'),
    endTime: endtDate.format('YYYY-MM-DD HH:mm:59')
  };

  const workloadData: any = await getWorkload(params.workload);
  const workload: Workload = workloadData.workload;
  const namespace = generateNamespace(workload.owner);

  let pods: WorkloadPod[] = [];
  if (workload.pods.length == 0) {
    const podsData = await getPodsByWorkload(params.workload, namespace);

    pods = podsData.pods.map((item: any) => {
      return {
        name: item,
        node: '',
        status: ''
      };
    });
  } else {
    pods = workload.pods;
  }

  return (
    <>
      <div className="container-fixed pb-[60px]">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
          <div className="flex flex-nowrap items-center justify-center gap-2">
            <Link href={`/admin/workload/list`} className="flex flex-nowrap items-center hover:text-primary">
              <IoIosArrowBack size={'1.3em'} />
            </Link>
            <h1 className="pl-1 text-xl font-semibold leading-none text-gray-900">워크로드 모니터링</h1>
          </div>
        </div>
        <WorkloadMonitoringHeader workload={workload}></WorkloadMonitoringHeader>
        <WorkloadMonitoringPanel workload={workload} usageRequest={usageRequest} pods={pods}></WorkloadMonitoringPanel>
      </div>
    </>
  );
}
