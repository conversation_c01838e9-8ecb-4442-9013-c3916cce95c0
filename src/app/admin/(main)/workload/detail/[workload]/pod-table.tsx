'use client';
import { Workload, WorkloadPod } from '@/types/workload';
import Link from 'next/link';
import PodLifeCycle from './pod-lifecycle';

interface PageProps {
  workload: Workload;
  namespace: string;
}
export default function PodsTable({ workload, namespace }: PageProps) {
  //Pod 상태 변경
  const convertPodStatus = (item: WorkloadPod) => {
    if (item != undefined && item != null) {
      if (item.status === 'Running') {
        return <span className="badge badge-sm badge-outline badge-success">실행</span>;
      } else if (item.status === 'Terminating') {
        return <span className="badge badge-sm badge-outline badge-warning">종료</span>;
      } else if (item.status === 'Failed') {
        return <span className="badge badge-sm badge-outline badge-danger">실패</span>;
      } else if (item.status === 'Succeeded') {
        return <span className="badge badge-sm badge-outline badge-info">성공</span>;
      } else if (item.status === 'Pending') {
        return <span className="badge badge-sm badge-outline badge-info">대기</span>;
      } else {
        return <span className="badge badge-sm badge-outline">{item.status}</span>;
      }
    } else {
      return '';
    }
  };

  /**
   * @brief 파드 컨테이널 로그, 컨테이너 터미널 링크 버튼
   * @param item
   * @returns
   */
  const convertPodButton = (item: WorkloadPod) => {
    if (item != undefined && item != null) {
      if (item.status === 'Running') {
        return (
          <>
            <Link className={`btn btn-light`} target="_blank" href={`/admin/workload/${workload?.ser}/pod/${item?.name}/logs`}>
              컨테이너 로그
            </Link>
            <Link
              className={`btn btn-light`}
              target="_blank"
              href={`/admin/workload/${workload?.ser}/pod/${item?.name}/terminal/${namespace}`}
            >
              컨테이너 터미널
            </Link>
          </>
        );
      } else {
        return (
          <>
            <button className={`btn disabled:bg-gray-400`} disabled={true}>
              컨테이너 로그
            </button>
            <button className={`btn disabled:bg-gray-400`} disabled={true}>
              컨테이너 터미널
            </button>
          </>
        );
      }
    } else {
      return '';
    }
  };

  return (
    <>
      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
        <div className="flex w-full justify-between">
          <div className="text-lg font-semibold text-gray-900">배포 상태</div>
          <div className="flex gap-3.5">
            <Link href={`/admin/workload/${workload.ser}/pod/dep${workload.ser}/events`} className="btn btn-sm btn-light">
              배포 이벤트
            </Link>
          </div>
        </div>
      </div>
      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
        <div className="min-w-full">
          <div className="scrollable-x-auto">
            <table className="table text-right">
              <thead>
                <tr>
                  <th className="min-w-[100px] text-nowrap text-center">노드</th>
                  <th className="min-w-[100px] text-nowrap text-center">파드</th>
                  <th className="min-w-[100px] text-nowrap text-center">상태</th>
                  <th className="min-w-[100px] text-nowrap text-center">시작시간</th>
                  <th className="text-center"></th>
                </tr>
              </thead>
              <tbody>
                {workload.pods.length > 0 &&
                  workload.pods.map((item, index) => (
                    <>
                      <tr key={`pods_${index}`}>
                        <td className="text-center">
                          <Link className="text-primary" href={`/admin/node/detail/${item.node.name}`}>
                            {item.node.name}
                          </Link>
                        </td>
                        <td className="text-nowrap text-center">{item.name}</td>
                        <td className="text-nowrap text-center">{convertPodStatus(item)}</td>
                        <td className="text-nowrap text-center">{item.startTime}</td>
                        <td className="flex gap-1.5 text-nowrap">{convertPodButton(item)}</td>
                      </tr>
                      <tr key={`pods_${index}`}>
                        <td colSpan={5}>
                          <div className="m-1 flex flex-col gap-2.5 border-[0.5px] border-gray-200 p-4">
                            <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                              <span className="font-bold">GPU</span> {item.node.gpuSpec}
                              <span className="font-bold">HOST</span> {item.node.hostSpec}
                              <span className="font-bold">그래픽 드라이버</span> {item.node.driver}
                              <span className="font-bold">CUDA</span> {item.node.cuda}
                              <span className="font-bold">네트워크 속도</span> 최근 {item.node.lastUpSpeed}Mbps 평균 {item.node.avgUpSpeed}Mbps
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr key={`pods_${index}`}>
                        <td colSpan={5}>
                          <PodLifeCycle owner={workload.owner} podName={item.name}></PodLifeCycle>
                        </td>
                      </tr>
                    </>
                  ))}
                {workload.pods.length == 0 && (
                  <tr>
                    <td colSpan={4} className="text-center">
                      <div className="flex flex-col items-center">
                        <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                        조회된 파드가 없습니다.
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
}
