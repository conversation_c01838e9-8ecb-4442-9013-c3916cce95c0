'use client';
import { getPodDeployEvents } from '@/action/pod-action';
import { PodEvent, PodEventResponse, PodDeployEventRequest } from '@/types/pod';
import { useEffect, useState, useRef } from 'react';

interface TimelineEvent {
  date: string;
  title: string;
  desc: string;
  type: string;
  reason: string;
  message: string;
}

interface PageProps {
  owner: string;
  podName: string;
}

export default function PodLifeCycle({ owner, podName }: PageProps) {
  const [eventsData, setEventsData] = useState<PodEvent[]>([]);
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const trackRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);
  const dotsRef = useRef<HTMLDivElement>(null);
  const labelsRef = useRef<HTMLDivElement>(null);

  const eventRequest: PodDeployEventRequest = {
    owner: owner,
    podName: podName,
    nodeName: '',
    startTime: '',
    endTime: ''
  };

  // PodEvent를 TimelineEvent로 변환
  const convertToTimelineEvents = (events: PodEvent[]): TimelineEvent[] => {
    return events
      .filter(event => event.firstTimestamp) // firstTimestamp가 있는 이벤트만 필터링
      .map(event => ({
        date: new Date(event.firstTimestamp).toISOString().split('T')[0], // YYYY-MM-DD 형식으로 변환
        title: event.reason || 'Unknown',
        desc: event.message || 'No description',
        type: event.type || 'Normal',
        reason: event.reason || '',
        message: event.message || ''
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()); // 날짜순 정렬
  };

  // 날짜를 퍼센트로 변환
  const dateToPercent = (dateStr: string, events: TimelineEvent[]): number => {
    if (events.length === 0) return 0;
    if (events.length === 1) return 50;

    const targetDate = new Date(dateStr).getTime();
    const minDate = new Date(events[0].date).getTime();
    const maxDate = new Date(events[events.length - 1].date).getTime();
    const spanMs = maxDate - minDate;

    if (spanMs === 0) return 50;
    return ((targetDate - minDate) / spanMs) * 100;
  };

  // 인덱스를 퍼센트로 변환
  const indexToPercent = (index: number): number => {
    if (timelineEvents.length === 0) return 0;
    return dateToPercent(timelineEvents[index].date, timelineEvents);
  };

  useEffect(() => {
    const init = async () => {
      const eventResponse: PodEventResponse = await getPodDeployEvents(eventRequest);
      if (eventResponse.status === 200) {
        setEventsData(eventResponse.events);
        const convertedEvents = convertToTimelineEvents(eventResponse.events);
        setTimelineEvents(convertedEvents);
        if (convertedEvents.length > 0) {
          setActiveIndex(0);
        }
      }
    };
    init();
  }, [owner, podName]);

  // 활성 이벤트 설정
  const setActive = (index: number) => {
    const newIndex = Math.max(0, Math.min(timelineEvents.length - 1, index));
    setActiveIndex(newIndex);

    if (timelineEvents.length === 0) return;

    const pct = indexToPercent(newIndex);

    if (progressRef.current) {
      progressRef.current.style.width = `${pct}%`;
    }
    if (handleRef.current) {
      handleRef.current.style.left = `${pct}%`;
    }
    if (trackRef.current) {
      trackRef.current.setAttribute('aria-valuenow', Math.round(pct).toString());
    }

    // 도트 활성화 상태 업데이트
    if (dotsRef.current) {
      const dots = dotsRef.current.children;
      Array.from(dots).forEach((dot, i) => {
        dot.classList.toggle('active', i === newIndex);
      });
    }
  };

  // 도트 렌더링
  const renderDots = () => {
    if (!dotsRef.current || !labelsRef.current) return;

    dotsRef.current.innerHTML = '';
    labelsRef.current.innerHTML = '';
    labelsRef.current.style.setProperty('--count', timelineEvents.length.toString());

    timelineEvents.forEach((event, i) => {
      const pct = dateToPercent(event.date, timelineEvents);

      // 도트 생성
      const dotEl = document.createElement('button');
      dotEl.className = 'dot';
      dotEl.style.left = `${pct}%`;
      dotEl.title = `${event.date} · ${event.title}`;
      dotEl.textContent = (i + 1).toString();
      dotEl.setAttribute('aria-label', `${event.date} ${event.title}로 이동`);
      dotEl.addEventListener('click', () => setActive(i));
      dotsRef.current!.appendChild(dotEl);

      // 라벨 생성
      const labelEl = document.createElement('div');
      labelEl.className = 'label';
      labelEl.textContent = event.date;
      labelsRef.current!.appendChild(labelEl);
    });
  };

  // 이벤트 타입에 따른 색상 반환
  const getEventTypeColor = (type: string): string => {
    switch (type.toLowerCase()) {
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  // 컴포넌트 마운트 후 도트 렌더링
  useEffect(() => {
    if (timelineEvents.length > 0) {
      setTimeout(() => {
        renderDots();
        setActive(0);
      }, 100);
    }
  }, [timelineEvents]);

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-2xl shadow-lg p-6" role="region" aria-label="Pod 라이프사이클 타임라인">
        <div className="flex items-center justify-between gap-3 mb-6">
          <h1 className="text-xl font-semibold">Pod 라이프사이클 - {podName}</h1>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium border border-gray-300 bg-white rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              onClick={() => setActive(activeIndex - 1)}
              disabled={activeIndex === 0}
              aria-label="이전 이벤트"
            >
              ◀︎ 이전
            </button>
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium border border-gray-300 bg-white rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              onClick={() => setActive(activeIndex + 1)}
              disabled={activeIndex === timelineEvents.length - 1}
              aria-label="다음 이벤트"
            >
              다음 ▶︎
            </button>
          </div>
        </div>

        {timelineEvents.length > 0 ? (
          <>
            <div className="relative py-7 px-0 mb-4">
              <div
                ref={trackRef}
                className="relative h-2 bg-gray-200 rounded-full shadow-inner"
                tabIndex={0}
                role="slider"
                aria-valuemin={0}
                aria-valuemax={100}
                aria-valuenow={0}
                aria-label="타임라인 진행률"
              >
                <div
                  ref={progressRef}
                  className="absolute left-0 top-0 h-2 bg-blue-500 rounded-full transition-all duration-300 ease-out"
                  style={{ width: '0%' }}
                />
                <div
                  ref={handleRef}
                  className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-5 h-5 bg-blue-500 border-3 border-white rounded-full shadow-lg cursor-pointer transition-all duration-300 ease-out"
                  style={{ left: '0%' }}
                />
                <div ref={dotsRef} className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-0" />
              </div>
              <div
                ref={labelsRef}
                className="grid gap-0 mt-3 text-xs text-gray-500 select-none"
                style={{ gridTemplateColumns: `repeat(${timelineEvents.length}, 1fr)` }}
              />
            </div>

            <div className="mt-5 p-4 border border-gray-200 rounded-xl bg-gray-50">
              {timelineEvents[activeIndex] && (
                <>
                  <h2 className="text-lg font-medium mb-2">
                    {timelineEvents[activeIndex].date} · {timelineEvents[activeIndex].title}
                  </h2>
                  <div className="space-y-2">
                    <p className="text-gray-700">{timelineEvents[activeIndex].desc}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className={`font-medium ${getEventTypeColor(timelineEvents[activeIndex].type)}`}>
                        타입: {timelineEvents[activeIndex].type}
                      </span>
                      <span className="text-gray-600">
                        사유: {timelineEvents[activeIndex].reason}
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            배포 이벤트 데이터가 없습니다.
          </div>
        )}
      </div>

      <style jsx>{`
        .dot {
          position: absolute;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background: white;
          border: 3px solid #e5e7eb;
          box-shadow: 0 2px 6px rgba(0,0,0,0.08);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          color: #111827;
          transition: all 0.2s ease;
        }

        .dot:hover {
          transform: translate(-50%, -50%) scale(1.1);
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .dot.active {
          border-color: #3b82f6;
          box-shadow: 0 6px 16px rgba(59,130,246,0.25);
          color: #3b82f6;
          background: white;
        }

        .label {
          text-align: center;
          white-space: nowrap;
        }

        @media (max-width: 640px) {
          .label {
            font-size: 10px;
          }
          .dot {
            width: 24px;
            height: 24px;
            font-size: 10px;
          }
        }
      `}</style>
    </div>
  );
}
