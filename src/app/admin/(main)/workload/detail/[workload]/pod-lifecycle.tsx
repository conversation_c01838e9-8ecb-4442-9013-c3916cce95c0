'use client';
import { getPodDeployEvents } from '@/action/pod-action';
import { PodEvent, PodEventResponse, PodDeployEventRequest } from '@/types/pod';
import { useEffect, useState, useRef, useCallback } from 'react';

interface TimelineEvent {
  date: string;
  title: string;
  desc: string;
  type: string;
  reason: string;
  message: string;
  kind: string;
  time: string;
}

interface PageProps {
  owner: string;
  podName: string;
}

export default function PodLifeCycle({ owner, podName }: PageProps) {
  const [eventsData, setEventsData] = useState<PodEvent[]>([]);
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const trackRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);
  const dotsRef = useRef<HTMLDivElement>(null);
  const labelsRef = useRef<HTMLDivElement>(null);

  const eventRequest: PodDeployEventRequest = {
    owner: owner,
    podName: podName,
    nodeName: '',
    startTime: '',
    endTime: ''
  };

  // PodEvent를 TimelineEvent로 변환 (최대 10개)
  const convertToTimelineEvents = useCallback((events: PodEvent[]): TimelineEvent[] => {
    return events
      .filter(event => event.firstTimestamp) // firstTimestamp가 있는 이벤트만 필터링
      .map(event => {
        const timestamp = new Date(event.firstTimestamp);
        return {
          date: timestamp.toISOString().split('T')[0], // YYYY-MM-DD 형식
          title: event.reason || 'Unknown',
          desc: event.message || 'No description',
          type: event.type || 'Normal',
          reason: event.reason || '',
          message: event.message || '',
          kind: event.kind || 'Pod',
          time: timestamp.toTimeString().split(' ')[0] // HH:MM:SS 형식
        };
      })
      .sort((a, b) => new Date(b.date + 'T' + b.time).getTime() - new Date(a.date + 'T' + a.time).getTime()) // 최신순 정렬
      .slice(0, 10) // 최대 10개만 선택
      .reverse(); // 다시 오래된 순으로 정렬 (타임라인 표시용)
  }, []);

  // 날짜시간을 퍼센트로 변환
  const dateToPercent = (dateTimeStr: string, events: TimelineEvent[]): number => {
    if (events.length === 0) return 0;
    if (events.length === 1) return 50;

    const targetDateTime = new Date(dateTimeStr).getTime();
    const minDateTime = new Date(events[0].date + 'T' + events[0].time).getTime();
    const maxDateTime = new Date(events[events.length - 1].date + 'T' + events[events.length - 1].time).getTime();
    const spanMs = maxDateTime - minDateTime;

    if (spanMs === 0) return 50;
    return ((targetDateTime - minDateTime) / spanMs) * 100;
  };

  // 인덱스를 퍼센트로 변환
  const indexToPercent = (index: number): number => {
    if (timelineEvents.length === 0) return 0;
    return dateToPercent(timelineEvents[index].date + 'T' + timelineEvents[index].time, timelineEvents);
  };

  useEffect(() => {
    const init = async () => {
      const eventResponse: PodEventResponse = await getPodDeployEvents(eventRequest);
      if (eventResponse.status === 200) {
        setEventsData(eventResponse.events);
        const convertedEvents = convertToTimelineEvents(eventResponse.events);
        setTimelineEvents(convertedEvents);
        if (convertedEvents.length > 0) {
          setActiveIndex(0);
        }
      }
    };
    init();
  }, [owner, podName, convertToTimelineEvents]);

  // 활성 이벤트 설정
  const setActive = useCallback((index: number) => {
    const newIndex = Math.max(0, Math.min(timelineEvents.length - 1, index));
    setActiveIndex(newIndex);

    if (timelineEvents.length === 0) return;

    const pct = indexToPercent(newIndex);

    if (progressRef.current) {
      progressRef.current.style.width = `${pct}%`;
    }
    if (handleRef.current) {
      handleRef.current.style.left = `${pct}%`;
    }
    if (trackRef.current) {
      trackRef.current.setAttribute('aria-valuenow', Math.round(pct).toString());
    }

    // 노드 활성화 상태 업데이트
    if (dotsRef.current) {
      const nodes = dotsRef.current.children;
      Array.from(nodes).forEach((node, i) => {
        node.classList.toggle('active', i === newIndex);
      });
    }
  }, [timelineEvents, indexToPercent]);

  // 노드 렌더링 (이미지 스타일)
  const renderNodes = useCallback(() => {
    if (!dotsRef.current || !labelsRef.current) return;

    dotsRef.current.innerHTML = '';
    labelsRef.current.innerHTML = '';
    labelsRef.current.style.setProperty('--count', timelineEvents.length.toString());

    timelineEvents.forEach((event, i) => {
      const pct = dateToPercent(event.date + 'T' + event.time, timelineEvents);

      // 노드 컨테이너 생성
      const nodeContainer = document.createElement('div');
      nodeContainer.className = 'node-container';
      nodeContainer.style.left = `${pct}%`;
      nodeContainer.setAttribute('aria-label', `${event.date} ${event.time} ${event.title}로 이동`);
      nodeContainer.addEventListener('click', () => setActive(i));

      // kind 텍스트 (노드 위)
      const kindEl = document.createElement('div');
      kindEl.className = 'node-kind';
      kindEl.textContent = event.kind;
      nodeContainer.appendChild(kindEl);

      // 원형 노드
      const nodeEl = document.createElement('div');
      nodeEl.className = 'node-circle';
      nodeContainer.appendChild(nodeEl);

      // 시간 텍스트 (노드 아래)
      const timeEl = document.createElement('div');
      timeEl.className = 'node-time';
      timeEl.textContent = event.time.substring(0, 5); // HH:MM 형식으로 표시
      nodeContainer.appendChild(timeEl);

      dotsRef.current!.appendChild(nodeContainer);

      // 날짜 라벨 생성 (최하단)
      const labelEl = document.createElement('div');
      labelEl.className = 'label';
      labelEl.textContent = event.date;
      labelsRef.current!.appendChild(labelEl);
    });
  }, [timelineEvents, dateToPercent, setActive]);

  // 이벤트 타입에 따른 색상 반환
  const getEventTypeColor = (type: string): string => {
    switch (type.toLowerCase()) {
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  // 컴포넌트 마운트 후 노드 렌더링
  useEffect(() => {
    if (timelineEvents.length > 0) {
      setTimeout(() => {
        renderNodes();
        setActive(0);
      }, 100);
    }
  }, [timelineEvents, renderNodes, setActive]);

  return (
    <div className="w-full p-6">
      <div className="bg-white rounded-2xl shadow-lg p-6" role="region" aria-label="Pod 라이프사이클 타임라인">
        <div className="flex items-center justify-between gap-3 mb-6">
          <h1 className="text-xl font-semibold">Pod 라이프사이클 - {podName}</h1>
          <div className="flex items-center gap-3">
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium border border-gray-300 bg-white rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => setActive(activeIndex - 1)}
              disabled={activeIndex === 0}
              aria-label="이전 이벤트"
            >
              ◀︎ 이전
            </button>
            <div className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl min-w-[120px] text-center">
              {timelineEvents.length > 0 && timelineEvents[activeIndex] ?
                timelineEvents[activeIndex].date :
                '--'
              }
            </div>
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium border border-gray-300 bg-white rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => setActive(activeIndex + 1)}
              disabled={activeIndex === timelineEvents.length - 1}
              aria-label="다음 이벤트"
            >
              다음 ▶︎
            </button>
          </div>
        </div>

        {timelineEvents.length > 0 ? (
          <>
            <div className="relative py-12 px-0 mb-4">
              <div
                ref={trackRef}
                className="relative h-2 bg-gray-200 rounded-full shadow-inner"
                tabIndex={0}
                role="slider"
                aria-valuemin={0}
                aria-valuemax={100}
                aria-valuenow={0}
                aria-label="타임라인 진행률"
              >
                <div
                  ref={progressRef}
                  className="absolute left-0 top-0 h-2 bg-blue-500 rounded-full transition-all duration-300 ease-out"
                  style={{ width: '0%' }}
                />
                <div
                  ref={handleRef}
                  className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-5 h-5 bg-blue-500 border-3 border-white rounded-full shadow-lg cursor-pointer transition-all duration-300 ease-out"
                  style={{ left: '0%' }}
                />
                <div ref={dotsRef} className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-0" />
              </div>
              <div
                ref={labelsRef}
                className="grid gap-0 mt-12 text-xs text-gray-500 select-none"
                style={{ gridTemplateColumns: `repeat(${timelineEvents.length}, 1fr)` }}
              />
            </div>

            <div className="mt-5 p-4 border border-gray-200 rounded-xl bg-gray-50">
              {timelineEvents[activeIndex] && (
                <>
                  <h2 className="text-lg font-medium mb-2">
                    {timelineEvents[activeIndex].date} · {timelineEvents[activeIndex].title}
                  </h2>
                  <div className="space-y-2">
                    <p className="text-gray-700">{timelineEvents[activeIndex].desc}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className={`font-medium ${getEventTypeColor(timelineEvents[activeIndex].type)}`}>
                        타입: {timelineEvents[activeIndex].type}
                      </span>
                      <span className="text-gray-600">
                        사유: {timelineEvents[activeIndex].reason}
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            배포 이벤트 데이터가 없습니다.
          </div>
        )}
      </div>

      <style jsx>{`
        .node-container {
          position: absolute;
          top: 50%;
          transform: translateX(-50%);
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          transition: all 0.2s ease;
        }

        .node-container:hover {
          transform: translateX(-50%) scale(1.05);
        }

        .node-container.active .node-circle {
          background: #3b82f6;
          border-color: #3b82f6;
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
        }

        .node-container.active .node-kind,
        .node-container.active .node-time {
          color: #3b82f6;
          font-weight: 600;
        }

        .node-kind {
          font-size: 11px;
          font-weight: 500;
          text-align: center;
          color: #374151;
          margin-bottom: 6px;
          white-space: nowrap;
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.2;
          background: white;
          padding: 2px 6px;
          border-radius: 12px;
          border: 1px solid #e5e7eb;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .node-circle {
          width: 16px;
          height: 16px;
          background: white;
          border: 3px solid #d1d5db;
          border-radius: 50%;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .node-time {
          font-size: 10px;
          font-weight: 400;
          text-align: center;
          color: #6b7280;
          margin-top: 6px;
          white-space: nowrap;
          line-height: 1.2;
        }

        .label {
          text-align: center;
          white-space: nowrap;
          font-size: 11px;
          color: #9ca3af;
        }

        @media (max-width: 640px) {
          .label {
            font-size: 10px;
          }
          .node-kind {
            font-size: 10px;
            max-width: 60px;
            padding: 1px 4px;
          }
          .node-time {
            font-size: 9px;
          }
          .node-circle {
            width: 14px;
            height: 14px;
            border-width: 2px;
          }
        }

        @media (max-width: 480px) {
          .node-kind {
            font-size: 9px;
            max-width: 50px;
          }
          .node-time {
            font-size: 8px;
          }
          .node-circle {
            width: 12px;
            height: 12px;
          }
        }
      `}</style>
    </div>
  );
}
