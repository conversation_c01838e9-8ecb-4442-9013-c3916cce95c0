'use client';
import { getPodDeployEvents } from '@/action/pod-action';
import { PodEvent, PodEventResponse, PodDeployEventRequest } from '@/types/pod';
import { useEffect, useState, useRef, useMemo } from 'react';

interface TimelineEvent {
  date: string;
  title: string;
  desc: string;
  type: string;
  reason: string;
  message: string;
  kind: string;
  time: string;
}

interface PageProps {
  owner: string;
  podName: string;
}

export default function PodLifeCycle({ owner, podName }: PageProps) {
  const [eventsData, setEventsData] = useState<PodEvent[]>([]);
  const [allTimelineEvents, setAllTimelineEvents] = useState<TimelineEvent[]>([]);
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const trackRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);
  const dotsRef = useRef<HTMLDivElement>(null);
  const labelsRef = useRef<HTMLDivElement>(null);

  const EVENTS_PER_PAGE = 10;

  const eventRequest = useMemo<PodDeployEventRequest>(() => ({
    owner: owner,
    podName: podName,
    nodeName: '',
    startTime: '',
    endTime: ''
  }), [owner, podName]);



  // 날짜시간을 퍼센트로 변환 (겹치는 시간 처리 포함)
  const dateToPercent = (dateTimeStr: string, events: TimelineEvent[], eventIndex: number): number => {
    if (events.length === 0) return 0;
    if (events.length === 1) return 50;

    const targetDateTime = new Date(dateTimeStr).getTime();
    const minDateTime = new Date(events[0].date + 'T' + events[0].time).getTime();
    const maxDateTime = new Date(events[events.length - 1].date + 'T' + events[events.length - 1].time).getTime();
    const spanMs = maxDateTime - minDateTime;

    // 최소 간격 보장 (이벤트 수에 따라 동적 조정)
    const minGapPercent = Math.max(10, 80 / events.length); // 최소 10% 간격

    if (spanMs === 0) {
      // 모든 이벤트가 같은 시간인 경우, 순차적으로 배치
      const totalWidth = Math.min(80, events.length * minGapPercent);
      const startPos = (100 - totalWidth) / 2; // 중앙 정렬
      const spacing = totalWidth / Math.max(events.length - 1, 1);
      return Math.max(5, Math.min(95, startPos + (spacing * eventIndex)));
    }

    let basePercent = ((targetDateTime - minDateTime) / spanMs) * 100;

    // 같은 시간대의 이벤트들을 찾아서 순차적으로 배치
    const sameTimeEvents = events.filter(e =>
      Math.abs(new Date(e.date + 'T' + e.time).getTime() - targetDateTime) < 1000 // 1초 이내
    );

    if (sameTimeEvents.length > 1) {
      const sameTimeIndex = sameTimeEvents.findIndex(e =>
        e.date === events[eventIndex].date &&
        e.time === events[eventIndex].time &&
        e.reason === events[eventIndex].reason
      );

      // 겹치는 이벤트들을 충분한 간격으로 오프셋하여 배치
      const spacing = Math.max(minGapPercent, 50 / sameTimeEvents.length);
      const offset = (sameTimeIndex - (sameTimeEvents.length - 1) / 2) * spacing;
      basePercent += offset;
    }

    return Math.max(5, Math.min(95, basePercent));
  };

  // 인덱스를 퍼센트로 변환
  const indexToPercent = (index: number): number => {
    if (timelineEvents.length === 0) return 0;
    return dateToPercent(timelineEvents[index].date + 'T' + timelineEvents[index].time, timelineEvents, index);
  };

  useEffect(() => {
    const init = async () => {
      const eventResponse: PodEventResponse = await getPodDeployEvents(eventRequest);
      if (eventResponse.status === 200) {
        setEventsData(eventResponse.events);

        console.log('=== API RESPONSE ===');
        console.log('Total events received:', eventResponse.events.length);
        console.log('Raw events sample:', eventResponse.events.slice(0, 3)); // 처음 3개만 표시

        // PodEvent를 TimelineEvent로 변환
        const convertedEvents = eventResponse.events
          .filter(event => event.firstTimestamp)
          .map(event => {
            const timestamp = new Date(event.firstTimestamp);
            return {
              date: timestamp.toISOString().split('T')[0],
              title: event.reason || 'Unknown',
              desc: event.message || 'No description',
              type: event.type || 'Normal',
              reason: event.reason || '',
              message: event.message || '',
              kind: event.kind || 'Pod',
              time: timestamp.toTimeString().split(' ')[0]
            };
          })
          .sort((a, b) => new Date(b.date + 'T' + b.time).getTime() - new Date(a.date + 'T' + a.time).getTime());

        console.log('Converted events:', convertedEvents.length); // 디버깅용
        console.log('Converted events data:', convertedEvents); // 변환된 데이터 확인
        setAllTimelineEvents(convertedEvents);

        // 총 페이지 수 계산
        const pages = Math.ceil(convertedEvents.length / EVENTS_PER_PAGE);
        setTotalPages(pages);

        console.log('=== PAGINATION INFO ===');
        console.log('Total events:', convertedEvents.length);
        console.log('Events per page:', EVENTS_PER_PAGE);
        console.log('Total pages:', pages);
        console.log('=======================');

        // 첫 페이지 이벤트 설정 - 최대 10개까지 표시
        if (convertedEvents.length > 0) {
          const eventsToShow = Math.min(convertedEvents.length, EVENTS_PER_PAGE);
          const firstPageEvents = convertedEvents.slice(0, eventsToShow);
          // 타임라인 표시를 위해 시간순으로 정렬 (오래된 것부터)
          const sortedForTimeline = [...firstPageEvents].sort((a, b) =>
            new Date(a.date + 'T' + a.time).getTime() - new Date(b.date + 'T' + b.time).getTime()
          );
          console.log('First page events:', sortedForTimeline.length); // 디버깅용
          console.log('Events to display:', sortedForTimeline); // 표시할 이벤트들
          setTimelineEvents(sortedForTimeline);
          setCurrentPage(0);
          setActiveIndex(0);
        }
      }
    };
    init();
  }, [owner, podName]);

  // 페이지 변경 함수
  const changePage = (newPage: number) => {
    if (newPage < 0 || newPage >= totalPages) return;

    const startIndex = newPage * EVENTS_PER_PAGE;
    const endIndex = Math.min(startIndex + EVENTS_PER_PAGE, allTimelineEvents.length);
    const pageEvents = allTimelineEvents.slice(startIndex, endIndex);

    // 타임라인 표시를 위해 시간순으로 정렬 (오래된 것부터)
    const sortedForTimeline = [...pageEvents].sort((a, b) =>
      new Date(a.date + 'T' + a.time).getTime() - new Date(b.date + 'T' + b.time).getTime()
    );

    console.log(`Page ${newPage + 1}: showing events ${startIndex} to ${endIndex - 1} (${sortedForTimeline.length} events)`); // 디버깅용
    console.log('Page events:', sortedForTimeline); // 페이지 이벤트들

    setTimelineEvents(sortedForTimeline);
    setCurrentPage(newPage);
    setActiveIndex(0);
  };

  // 이전/다음 페이지 함수
  const goToPreviousPage = () => {
    changePage(currentPage - 1);
  };

  const goToNextPage = () => {
    changePage(currentPage + 1);
  };

  // 활성 이벤트 설정
  const setActive = (index: number) => {
    const newIndex = Math.max(0, Math.min(timelineEvents.length - 1, index));
    setActiveIndex(newIndex);

    if (timelineEvents.length === 0) return;

    const pct = indexToPercent(newIndex);

    if (progressRef.current) {
      progressRef.current.style.width = `${pct}%`;
    }
    if (handleRef.current) {
      handleRef.current.style.left = `${pct}%`;
    }
    if (trackRef.current) {
      trackRef.current.setAttribute('aria-valuenow', Math.round(pct).toString());
    }
  };

  // 노드 렌더링 (이미지 스타일)
  const renderNodes = () => {
    if (!dotsRef.current || !labelsRef.current) return;

    dotsRef.current.innerHTML = '';
    labelsRef.current.innerHTML = '';

    // 고유한 날짜 수 계산
    const uniqueDates = [...new Set(timelineEvents.map(event => event.date))];
    labelsRef.current.style.setProperty('--count', uniqueDates.length.toString());

    timelineEvents.forEach((event, i) => {
      const pct = dateToPercent(event.date + 'T' + event.time, timelineEvents, i);

      // 노드 컨테이너 생성
      const nodeContainer = document.createElement('div');
      nodeContainer.className = 'absolute top-1/2 -translate-x-1/2 cursor-pointer flex flex-col items-center transition-all duration-200 ease-in-out hover:scale-105';
      nodeContainer.style.left = `${pct}%`;
      nodeContainer.setAttribute('aria-label', `${event.date} ${event.time} ${event.title}로 이동`);
      nodeContainer.addEventListener('click', () => setActive(i));

      // 같은 시간대 이벤트 확인
      const currentTime = new Date(event.date + 'T' + event.time).getTime();
      const sameTimeEvents = timelineEvents.filter(e =>
        Math.abs(new Date(e.date + 'T' + e.time).getTime() - currentTime) < 1000
      );
      const isOverlapping = sameTimeEvents.length > 1;

      // reason 텍스트 (노드 위)
      const reasonEl = document.createElement('div');
      let reasonClass = 'text-xs font-semibold text-center text-gray-800 mb-2 whitespace-nowrap max-w-20 overflow-hidden text-ellipsis leading-tight bg-white px-1.5 py-0.5 rounded-md border border-gray-300 shadow-md';

      // 겹치는 이벤트인 경우 시각적 구분
      if (isOverlapping) {
        reasonClass = 'text-xs font-semibold text-center text-amber-800 mb-2 whitespace-nowrap max-w-20 overflow-hidden text-ellipsis leading-tight bg-amber-50 px-1.5 py-0.5 rounded-md border border-amber-300 shadow-md';
      }

      reasonEl.className = reasonClass;
      reasonEl.textContent = event.reason || event.kind;
      nodeContainer.appendChild(reasonEl);

      // 원형 노드
      const nodeEl = document.createElement('div');
      let nodeClass = 'w-5 h-5 bg-white border-3 border-gray-400 rounded-full transition-all duration-200 shadow-md';

      // 겹치는 이벤트인 경우 다른 색상
      if (isOverlapping) {
        nodeClass = 'w-5 h-5 bg-amber-100 border-3 border-amber-500 rounded-full transition-all duration-200 shadow-md';
      }

      nodeEl.className = nodeClass;
      nodeContainer.appendChild(nodeEl);

      // 시간 텍스트 (노드 아래)
      const timeEl = document.createElement('div');
      let timeClass = 'text-xs font-medium text-center text-gray-700 mt-2 whitespace-nowrap leading-tight';

      // 겹치는 이벤트인 경우 강조
      if (isOverlapping) {
        timeClass = 'text-xs font-semibold text-center text-amber-800 mt-2 whitespace-nowrap leading-tight';
      }

      timeEl.className = timeClass;
      timeEl.textContent = event.time; // HH:MM:SS 형식으로 표시 (초까지)
      nodeContainer.appendChild(timeEl);

      // 활성 상태 처리
      if (i === activeIndex) {
        nodeContainer.classList.add('active');
        nodeEl.className = 'w-6 h-6 bg-blue-500 border-3 border-blue-600 rounded-full transition-all duration-200 shadow-lg ring-4 ring-blue-200';
        reasonEl.className = 'text-xs font-bold text-center text-blue-700 mb-2 whitespace-nowrap max-w-20 overflow-hidden text-ellipsis leading-tight bg-blue-50 px-1.5 py-0.5 rounded-md border-2 border-blue-300 shadow-lg';
        timeEl.className = 'text-xs font-bold text-center text-blue-700 mt-2 whitespace-nowrap leading-tight';
      }

      dotsRef.current!.appendChild(nodeContainer);
    });

    // 날짜 라벨 생성 (중복 제거하여 하나만 표시)
    uniqueDates.forEach(date => {
      const labelEl = document.createElement('div');
      labelEl.className = 'text-center whitespace-nowrap text-sm text-gray-600 font-medium';
      labelEl.textContent = date;
      labelsRef.current!.appendChild(labelEl);
    });
  };

  // 이벤트 타입에 따른 색상 반환
  const getEventTypeColor = (type: string): string => {
    switch (type.toLowerCase()) {
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  // 페이지 변경 시 노드 렌더링
  useEffect(() => {
    if (timelineEvents.length > 0) {
      setTimeout(() => {
        renderNodes();
      }, 100);
    }
  }, [timelineEvents, activeIndex]);

  return (
    <div className="w-full p-6">
      <div className="bg-white rounded-2xl shadow-lg p-6" role="region" aria-label="Pod 라이프사이클 타임라인">
        <div className="flex items-center justify-between gap-3 mb-6">
          <h1 className="text-xl font-semibold">Pod 라이프사이클 - {podName}</h1>
          <div className="flex items-center gap-3">
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium border border-gray-300 bg-white rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={goToPreviousPage}
              disabled={currentPage === 0}
              aria-label="이전 페이지"
            >
              ◀︎ 이전
            </button>
            <div className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl min-w-[120px] text-center">
              {totalPages > 0 ? `${currentPage + 1} / ${totalPages}` : '--'}
            </div>
            <button
              type="button"
              className="px-3 py-2 text-sm font-medium border border-gray-300 bg-white rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={goToNextPage}
              disabled={currentPage >= totalPages - 1}
              aria-label="다음 페이지"
            >
              다음 ▶︎
            </button>
          </div>
        </div>

        {timelineEvents.length > 0 ? (
          <>
            <div className="relative py-20 px-0 mb-4">
              <div
                ref={trackRef}
                className="relative h-2 bg-gray-200 rounded-full shadow-inner"
                tabIndex={0}
                role="slider"
                aria-valuemin={0}
                aria-valuemax={100}
                aria-valuenow={0}
                aria-label="타임라인 진행률"
              >
                <div
                  ref={progressRef}
                  className="absolute left-0 top-0 h-2 bg-blue-500 rounded-full transition-all duration-300 ease-out"
                  style={{ width: '0%' }}
                />
                <div
                  ref={handleRef}
                  className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-5 h-5 bg-blue-500 border-3 border-white rounded-full shadow-lg cursor-pointer transition-all duration-300 ease-out"
                  style={{ left: '0%' }}
                />
                <div ref={dotsRef} className="absolute left-0 right-0 top-1/2 h-0" />
              </div>
              <div
                ref={labelsRef}
                className="grid gap-0 mt-20 text-sm text-gray-600 select-none font-medium"
                style={{ gridTemplateColumns: `repeat(var(--count, 1), 1fr)` }}
              />
            </div>

            <div className="mt-5 p-4 border border-gray-200 rounded-xl bg-gray-50">
              {timelineEvents[activeIndex] && (
                <>
                  <div className="flex items-center justify-between mb-2">
                    <h2 className="text-lg font-medium">
                      {timelineEvents[activeIndex].date} · {timelineEvents[activeIndex].title}
                    </h2>
                    <div className="text-sm text-gray-500">
                      페이지 {currentPage + 1}/{totalPages} | 전체 {allTimelineEvents.length}개 이벤트 | 현재 페이지 {timelineEvents.length}개
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-gray-700">{timelineEvents[activeIndex].desc}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className={`font-medium ${getEventTypeColor(timelineEvents[activeIndex].type)}`}>
                        타입: {timelineEvents[activeIndex].type}
                      </span>
                      <span className="text-gray-600">
                        사유: {timelineEvents[activeIndex].reason}
                      </span>
                      <span className="text-gray-600">
                        시간: {timelineEvents[activeIndex].time}
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {allTimelineEvents.length === 0 ?
              '배포 이벤트 데이터가 없습니다.' :
              '현재 페이지에 표시할 이벤트가 없습니다.'
            }
          </div>
        )}
      </div>


    </div>
  );
}
