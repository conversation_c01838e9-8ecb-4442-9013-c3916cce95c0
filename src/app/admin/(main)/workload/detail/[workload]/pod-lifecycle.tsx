'use client';
import { getPodDeployEvents } from '@/action/pod-action';
import { PodEvent, PodEventResponse, PodDeployEventRequest } from '@/types/pod';
import { useEffect, useState } from 'react';


interface PageProps {
  owner: string;
  podName: string;
}
export default function PodLifeCycle({ owner, podName }: PageProps) {
  const [eventsData, setEventsData] = useState<PodEvent[]>([]);
  let eventRequest: PodDeployEventRequest = {
    owner: owner,
    podName: podName,
    nodeName: '',
    startTime: '',
    endTime: ''
  };

  useEffect(() => {
    const init = async () => {
      const eventResponse: PodEventResponse = await getPodDeployEvents(eventRequest);
      if (eventResponse.status == 200) {
        setEventsData(eventResponse.events);
      }
    };
    init();
  }, []);

  return (
    <>

    </>
  );
}
