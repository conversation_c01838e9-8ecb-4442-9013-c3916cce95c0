import { IoIosArrowBack } from 'react-icons/io';

import { comma, dateTime, isBlank, utcTime } from '@/utils';
import { getWorkload } from '@/action/workload-action';
import { getUser } from '@/action/user-action';
import { Workload, WorkloadPod } from '@/types/workload';
import { User } from '@/types/user';
import PodsTable from './pod-table';
import Link from 'next/link';
import React from 'react';

interface PageProps {
  params: {
    workload: string;
  };
}
export default async function WorkloadInfoPage({ params }: PageProps) {
  const workloadData: any = await getWorkload(params.workload);
  const workload: Workload = workloadData.workload;
  let user: User;
  if (workload !== undefined) {
    const userData: any = await getUser(workload.owner);
    user = userData.user;
  }

  // Gpu 이름 변경
  const convertGpuName = (item: Workload) => {
    if (item != undefined && item != null) {
      if (item.gpu.trim().length > 0) {
        return (
          <>
            <img src="/da/img/ico_nvidia.png" style={{ verticalAlign: 'middle' }} className="h-[14px] w-[14px]" alt="" />{' '}
            {item.gpu.toUpperCase() + ' x ' + item.gpu_count}
          </>
        );
      } else {
        return <>전체</>;
      }
    } else {
      return '';
    }
  };

  /**
   * @brief Target 이름변경 및 아이콘 설정
   * @param item
   * @returns
   */
  const convertTargetName = (item: Workload) => {
    if (item != undefined && item != null) {
      if (item.target === 'pc') {
        return (
          <>
            <img src="/da/img/ico_pc.svg" alt="" />
            Tier 3
          </>
        );
      } else if (item.target === 'svr') {
        return (
          <>
            <img src="/da/img/ico_server.svg" alt="" />
            Tier 2
          </>
        );
      } else if (item.target === 'csp') {
        return (
          <>
            <img src="/da/img/ico_cloud.svg" alt="" />
            Tier 1
          </>
        );
      } else {
        return <>전체</>;
      }
    } else {
      return '';
    }
  };

  /**
   * @brief Category 이름변경
   * @param item
   * @returns
   */
  const convertCategoryName = (item: Workload) => {
    if (item != undefined && item != null) {
      if (item.category == 'infer') {
        return '추론';
      } else if (item.category == 'learn') {
        return '학습';
      } else if (item.category == 'job') {
        return '일괄작업';
      }
    } else {
      return '';
    }
  };

  /**
   * @brief 상태변경
   * @param item
   * @returns
   */
  const convertState = (item: Workload) => {
    if (item != undefined && item != null) {
      if (item.state == 'open') {
        return <span className="badge-gray badge badge-sm badge-outline">미배포</span>;
      } else if (item.state == 'deploy') {
        return <span className="badge badge-sm badge-outline badge-success">배포</span>;
      } else if (item.state == 'finish') {
        return <span className="badge badge-sm badge-outline badge-danger">종료</span>;
      }
    } else {
      return '';
    }
  };

  /**
   * @brief Repo 변경
   * @param item
   * @returns
   */
  const convertRepo = (item: Workload) => {
    if (item.repo === 'docker.io') {
      return (
        <div className="flex flex-nowrap gap-2">
          <img src="/da/img/ico_dockerhub_blue.svg" className="w-[18px]" alt="" /> Docker Hub
        </div>
      );
    } else if (item.repo === 'nvcr.io') {
      return (
        <div className="flex flex-nowrap gap-2">
          <img src="/da/img/ico_nvidia.svg" className="w-[18px]" alt="" /> NVIDIA
        </div>
      );
    } else if (item.repo === 'ghcr.io') {
      return (
        <div className="flex flex-nowrap gap-2">
          <img src="/da/img/ico_github.svg" className="w-[18px]" alt="" /> GitHub
        </div>
      );
    } else if (item.repo === 'quay.io') {
      return (
        <div className="flex flex-nowrap gap-2">
          <img src="/da/img/ico_redhat.svg" className="w-[18px]" alt="" /> Red Hat Quay
        </div>
      );
    } else if (item.repo === 'registry.hf.space') {
      return (
        <div className="flex flex-nowrap gap-2">
          <img src="/da/img/ico_huggingface.svg" className="w-[18px]" alt="" /> Hugging Face
        </div>
      );
    } else {
      return '';
    }
  };
  const convertCuda = (item: string) => {
    if (item === '12000') {
      return '12.0.0';
    } else if (item === '12010') {
      return '12.1.0';
    } else if (item === '12020') {
      return '12.2.0';
    } else if (item === '12030') {
      return '12.3.0';
    } else if (item === '12040') {
      return '12.4.0';
    } else if (item === '12050') {
      return '12.5.0';
    } else if (item === '12060') {
      return '12.6.0';
    } else if (item === '12070') {
      return '12.7.0';
    } else if (item === '12080') {
      return '12.8.0';
    } else if (item === '12090') {
      return '12.9.0';
    } else {
      return item;
    }
  };

  const convertEnvs = (evnStr: any) => {
    const envList = isBlank(evnStr) ? ([] as any[]) : (JSON.parse(evnStr) as []);

    return (
      <>
        <div className="flex grow flex-col gap-2">
          {
            envList
              .map((item: any, index: number) => {
                const [key, value] = Object.entries(item)[0];

                if (!isBlank(key)) {
                  return (
                    <div className="flex w-full gap-2" key={`env_key_${index}`}>
                      <span className="input !w-full !max-w-[180px] !text-[12px]">{key}</span>
                      <span className="input">{value as string}</span>
                    </div>
                  );
                } else {
                  return null;
                }
              })
              .filter(Boolean) as []
          }
        </div>
      </>
    );
  };

  return (
    <>
      {workload !== undefined ? (
        <div className="container-fixed pb-[60px]">
          <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
            <div className="flex flex-nowrap items-center justify-center gap-2">
              <Link href={`/admin/workload/list`} className="flex flex-nowrap items-center hover:text-primary">
                <IoIosArrowBack size={'1.3em'} />
              </Link>
              <h1 className="pl-1 text-xl font-semibold leading-none text-gray-900">워크로드 정보 {' #' + workload?.ser}</h1>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-5 lg:grid-cols-1 lg:gap-7.5">
            <div className="col-span-1">
              <div className="flex flex-col gap-5 lg:gap-7.5">
                <div className="card min-w-full">
                  <div className="card-body gap-8 py-5 lg:py-7.5">
                    <div className="flex flex-col gap-6">
                      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full">
                          <div className="text-lg font-medium text-gray-900">수요자</div>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">이메일</div>
                          <span className="font-semibold text-gray-900">{user.email}</span>
                        </div>

                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">이름</div>
                          <span className="font-semibold text-gray-900">{user.name}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full items-center text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">기업명</div>
                          <span className="font-semibold text-gray-900">{user.company}</span>
                        </div>

                        <div className="flex w-full items-center text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">직무/역할</div>
                          <span className="font-semibold text-gray-900">{user.jobPosition}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full items-center text-[14px] leading-[14px]">
                          <div className="w-40 text-sm font-medium text-gray-500">네임스페이스</div>
                          <span className="font-semibold text-gray-900">{user.namespace}</span>
                        </div>
                      </div>

                      <div className="my-4 border-t border-gray-200"></div>

                      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full">
                          <div className="text-lg font-medium text-gray-900">개요</div>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">워크로드 번호</div>
                          <span className="font-semibold text-gray-900">{workload?.ser}</span>
                        </div>

                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">설명</div>
                          <span className="font-semibold text-gray-900">{workload?.description}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full items-center text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">유형</div>
                          <span className="font-semibold text-gray-900">{convertCategoryName(workload)}</span>
                        </div>

                        <div className="flex w-full items-center text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">상태</div>
                          <span className="font-semibold text-gray-900">{convertState(workload)}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full items-center text-[14px] leading-[14px]">
                          <div className="w-40 text-sm font-medium text-gray-500">서비스 URL</div>
                          {workload?.state == 'deploy' && (
                            <Link href={workload?.svc_url} target="_blank">
                              <span className="flex items-center font-semibold text-primary">{workload?.svc_url}</span>{' '}
                            </Link>
                          )}
                          {workload?.state != 'deploy' && <span className="font-semibold text-gray-900">{workload?.svc_url}</span>}
                        </div>
                      </div>

                      <div className="my-4 border-t border-gray-200"></div>

                      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full">
                          <div className="text-lg font-medium text-gray-900">컨테이너</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2 lg:gap-14">
                        <div className="flex flex-col gap-6">
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">컨테이너 이미지</div>
                              <span className="font-semibold text-gray-900">{workload?.cont_image}</span>
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">컨테이너 포트</div>
                              <span className="font-semibold text-gray-900">{workload?.port}</span>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">저장소 유형</div>
                              <span className="font-semibold text-gray-900">{convertRepo(workload)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col gap-6">
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">생성일시</div>
                              <span className="text-2sm font-semibold text-gray-900">{dateTime(workload?.created_at)}</span>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">배포일시</div>
                              <span className="text-2sm font-semibold text-gray-900">{dateTime(workload?.deploy_at)}</span>
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">종료일시</div>
                              <span className="font-semibold text-gray-900">{dateTime(workload?.close_at)}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="my-4 border-t border-gray-200"></div>

                      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full">
                          <div className="text-lg font-medium text-gray-900">목적스펙</div>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="flex w-40 flex-nowrap items-center gap-1.5 font-medium text-gray-500">목적 노드</div>
                          <span className="flex flex-nowrap items-center gap-1.5 font-semibold text-gray-900">
                            {convertTargetName(workload)}
                          </span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full flex-nowrap text-[14px] leading-[14px]">
                          <div className="flex w-40 flex-nowrap items-center gap-1.5 font-medium text-gray-500">GPU</div>
                          <span className="flex w-auto flex-nowrap items-center gap-1.5 font-semibold text-gray-900">
                            {convertGpuName(workload)}
                          </span>
                        </div>
                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="flex w-40 flex-nowrap items-center gap-1.5 font-medium text-gray-500">GPU 메모리 (GB)</div>
                          <span className="font-semibold text-gray-900">{workload?.vram}</span>
                        </div>
                      </div>

                      <div className="my-4 border-t border-gray-200"></div>

                      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full">
                          <div className="text-lg font-medium text-gray-900">옵션</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2 lg:gap-14">
                        <div className="flex flex-col gap-6">
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">컨테이너 명령</div>
                              <span className="font-semibold text-gray-900">{workload?.cont_cmd}</span>
                            </div>
                          </div>
                          <div className="flex w-full text-[14px] leading-[14px]">
                            <div className="flex w-40 flex-nowrap items-center gap-1.5 font-medium text-gray-500">레플리카</div>
                            <span className="font-semibold text-gray-900">{workload?.replica}</span>
                          </div>

                          <div className="flex w-full flex-nowrap text-[14px] leading-[14px]">
                            <div className="flex w-40 flex-nowrap items-center gap-1.5 font-medium text-gray-500">최소 CUDA 버전</div>
                            <span className="flex flex-nowrap gap-1.5 font-semibold text-gray-900">{convertCuda(workload?.cuda)}</span>
                          </div>
                        </div>
                        <div className="flex flex-col gap-6">
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">컨테이너 환경변수</div>
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">{convertEnvs(workload?.cont_envs)}</div>
                          </div>
                        </div>
                      </div>

                      {workload?.pv_capa > 0 && workload?.mount_path.length > 0 && (
                        <>
                          <div className="my-4 border-t border-gray-200"></div>

                          <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="text-lg font-medium text-gray-900">영구 저장소</div>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">용량 (MB)</div>
                              <span className="font-semibold text-gray-900">{comma(workload?.pv_capa)}</span>
                            </div>

                            <div className="flex w-full text-[14px] leading-[14px]">
                              <div className="w-40 font-medium text-gray-500">마운트 경로</div>
                              <span className="font-semibold text-gray-900">{workload?.mount_path}</span>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                            <div className="flex w-full">
                              <div className="w-40 font-medium text-gray-500">클래스</div>
                              <span className="font-semibold text-gray-900">{workload?.pv_class}</span>
                            </div>
                          </div>
                        </>
                      )}
                      <div className="my-4 border-t border-gray-200"></div>
                      <PodsTable workload={workload} namespace={user.namespace}></PodsTable>

                      <div className="my-4 border-t border-gray-200"></div>

                      <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full">
                          <div className="text-lg font-medium text-gray-900">운영 현황</div>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">최근배포일</div>
                          <span className="font-semibold text-gray-900">
                            {workload.pods.length > 0 && <>{workload.pods[0].startTime}</>}
                            {workload.pods.length == 0 && <>--</>}
                          </span>
                        </div>

                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">현재 연결 노드명</div>
                          <span className="font-semibold text-gray-900">
                            {workload.pods.length > 0 && <>{workload.pods[0].node.name}</>}
                            {workload.pods.length == 0 && <>--</>}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-5 md:flex-nowrap lg:gap-14">
                        <div className="flex w-full text-[14px] leading-[14px]">
                          <div className="w-40 font-medium text-gray-500">지출 현황</div>
                          <span className="font-semibold text-gray-900">
                            총 지출 {comma(workload.spend.krwAmount)}원, 기본 지출 {comma(workload.spend.krwUnitAmount)}원, 이용 지출{' '}
                            {comma(workload.spend.krwUsageAmount)}원
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="container-fixed pb-[60px]">
          <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
            <div className="flex flex-nowrap items-center justify-center gap-2">
              <Link href={`/admin/workload/list`} className="flex flex-nowrap items-center hover:text-primary">
                <IoIosArrowBack size={'1.3em'} />
              </Link>
              <h1 className="pl-1 text-xl font-semibold leading-none text-gray-900">워크로드 정보 {' #' + params.workload}</h1>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-5 lg:grid-cols-1 lg:gap-7.5">
            <div className="col-span-1">
              <div className="flex flex-col gap-5 lg:gap-7.5">
                <div className="card min-w-full">
                  <div className="card-body gap-8 py-5 lg:py-7.5">
                    <div className="flex flex-col items-center p-10">
                      <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                      존재하지 않는 워크로드 입니다.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
  // }
}
