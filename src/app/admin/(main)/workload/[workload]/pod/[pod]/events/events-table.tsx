'use client';

import { getPodEvents } from '@/action/pod-action';
import { Paging } from '@/types/paging';
import { PodEvent, PodEventResponse } from '@/types/pod';
import { dateTime, dateTime2, mutcTime } from '@/utils';

import React, { MouseEvent, Suspense, useEffect, useState } from 'react';
import { FaMinus, FaPlus } from 'react-icons/fa6';

interface PageProps {
  data: PodEvent[];
  paging: Paging;
  requestParams: any;
  onRequestSearch: (startNum: number, sacleNum: number) => void;
}
export default function PodEventTable({ data, paging, requestParams, onRequestSearch }: PageProps) {
  const [extendRow, setExtendRow] = useState<boolean[]>([]);

  useEffect(() => {
    const initialExtendRow = Array(data.length).fill(false);
    setExtendRow(initialExtendRow);
  }, [data]);

  /**
   * @brief 응답유형 변환
   * @param item
   * @returns
   */
  const convertResType = (item: string) => {
    if (item == 'ADDED') {
      return '등록';
    } else if (item == 'MODIFIED') {
      return '수장';
    } else if (item == 'DELETED') {
      return '삭제';
    } else {
      return '';
    }
  };

  /**
   * @brief 이벤트 유형 변환
   * @param item
   * @returns
   */
  const convertType = (item: string) => {
    if (item == 'Normal') {
      return <span>일반</span>;
    } else if (item == 'Warning') {
      return <span className="text-warning">경고</span>;
    } else {
      return '';
    }
  };

  /**
   * @brier 조회
   * @param startNum
   */
  const search = (startNum: number) => {
    onRequestSearch(startNum, requestParams.scaleNum);
  };

  return (
    <>
      <div className="grid">
        <div className="card min-w-full">
          <div className="card-body gap-8 py-5 lg:py-7.5">
            <div className="min-w-full">
              <div className="scrollable-x-auto">
                <table className="stable align-middle">
                  <thead className="bg-[#000000]">
                    <tr className="text-nowrap text-left">
                      <th className="text-left !font-semibold">응답 유형</th>
                      <th className="!font-semibold">이벤트 유형</th>
                      <th className="!font-semibold">이벤트 이유</th>
                      <th className="!font-semibold">보고 구성 요소</th>
                      <th className="!font-semibold">이벤트 대상 유형</th>
                      <th className="!font-semibold">발생횟수</th>
                      <th className="!font-semibold">최초발생일시</th>
                      <th className="!font-semibold">최근발생일시</th>
                      <th className="text-center !font-semibold">상세보기</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.length > 0 &&
                      data.map((item, index) => (
                        <React.Fragment key={`event_${index}`}>
                          <tr key={`pod_event_${index}`} className="text-nowrap text-left">
                            <td className="text-left">{convertResType(item.resType)}</td>
                            <td className="text-left">{convertType(item.type)}</td>
                            <td>{item.reason}</td>
                            <td>{item.reportingComponent}</td>
                            <td>{item.kind}</td>
                            <td>{item.count}</td>
                            <td>{dateTime2(item.firstTimestamp)}</td>
                            <td>{dateTime2(item.lastTimestamp)}</td>

                            <td className="text-center">
                              <button
                                className="btn-icon btn btn-sm btn-clear btn-light"
                                data-index={index}
                                onClick={(e: MouseEvent<HTMLButtonElement>) => {
                                  const idx = Number(e.currentTarget.getAttribute('data-index'));
                                  setExtendRow((prevData) => {
                                    const updateForm = [...prevData];
                                    updateForm[idx] = !prevData[idx];
                                    return updateForm;
                                  });
                                }}
                              >
                                {extendRow[index] ? <FaMinus /> : <FaPlus />}
                              </button>
                            </td>
                          </tr>
                          <tr key={`pod_event_message_${index}`} className={`${extendRow[index] ? 'show' : 'hidden'}`}>
                            <td colSpan={9} className="bg-gray-100">
                              <div className="py-5 text-center">
                                <pre className="text-wrap text-balance break-all">{item.message}</pre>
                              </div>
                            </td>
                          </tr>
                        </React.Fragment>
                      ))}

                    {data.length == 0 && (
                      <tr>
                        <td colSpan={9} className="text-center">
                          <div className="flex flex-col items-center p-10">
                            <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                            조회되 이벤트가 없습니다.
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
            {paging != null && data.length > 0 && (
              <div className="flex min-w-full justify-center py-5">
                <div className="pagination">
                  <button className={`btn`} disabled={paging.isPrevPage == 'true' ? false : true} onClick={() => search(0)}>
                    <i className="ki-outline ki-double-left"></i>
                  </button>
                  <button className="btn" disabled={paging.isPrevPage == 'true' ? false : true} onClick={() => search(paging.prevNum)}>
                    <i className="ki-outline ki-left"></i>
                  </button>
                  {paging.pages.map((item, index) => (
                    <button
                      className={`${item.pageNum == paging.currentPageNum ? 'btn-primary' : ''} btn btn-outline`}
                      onClick={() => search(item.startNum)}
                      key={`key_${index}`}
                    >
                      {item.pageNum}
                    </button>
                  ))}

                  <button className="btn" disabled={paging.isNextPage == 'true' ? false : true} onClick={() => search(paging.nextNum)}>
                    <i className="ki-outline ki-right"></i>
                  </button>
                  <button className="btn" disabled={paging.isNextPage == 'true' ? false : true} onClick={() => search(paging.lastNum)}>
                    <i className="ki-outline ki-double-right"></i>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
