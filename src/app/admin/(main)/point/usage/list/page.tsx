import { getPointBase, getPointUsageList } from '@/action/point-action';
import { PointBase, PointHistory, PointUsageSearch, PointUsageResponse } from '@/types/point';
import { Paging } from '@/types/paging';
import PointUsageTable from './usage-table.tsx';

type PageProps = {
  searchParams: { [key: string]: string | string[] | undefined };
};

export default async function PointUsagePage({ searchParams }: PageProps) {
  let pointUsageSearch: PointUsageSearch = {
    startNum: 0,
    scaleNum: 15,
    owner: '',
    nodeOwner: '',
    nodeName: '',
    workload: 0,
    podName: '',
    gpuName: '',
    startTime: '',
    endTime: '',
    sortName: 'start_at',
    sortType: 'DESC'
  };
  const pointBaseResponse = await getPointBase();
  let pointBase: PointBase = pointBaseResponse.data as PointBase;
  let usageList: PointHistory[] = [];
  let paging: Paging | null = null;

  const search = async () => {
    for (const key in searchParams) {
      if (searchParams[key] !== undefined && key in pointUsageSearch) {
        // @ts-ignore
        pointUsageSearch[key] = searchParams[key];
      }
    }

    const response: PointUsageResponse = await getPointUsageList(pointUsageSearch);
    if (response.status == 200) {
      usageList = response.pointUsages;
      paging = response.paging as Paging;
    }
  };

  await search();

  return (
    <>
      <div className="container-fixed">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-7.5 lg:items-end">
          <div className="flex flex-col justify-center gap-2">
            <h1 className="text-xl font-semibold leading-none text-gray-900">포인트 과금 내역</h1>
          </div>
        </div>
        <PointUsageTable usageList={usageList} paging={paging} usageSearch={pointUsageSearch} pointBase={pointBase}></PointUsageTable>
      </div>
    </>
  );
}
