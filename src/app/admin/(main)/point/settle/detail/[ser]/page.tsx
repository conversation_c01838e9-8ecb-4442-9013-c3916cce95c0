import { getUser } from '@/action/user-action';
import { getPointSettle } from '@/action/point-action';
import Page5xx from '@/components/page_5xx';
import { isBlank } from '@/utils';
import UserProfileComponent from './user-profile';
import SettleInfoComponent from './settle-info';
import UserSettleListComponent from './user-settle-list';
import { IoIosArrowBack } from 'react-icons/io';
import Link from 'next/link';

export default async function UserSettlePage({ params }: { params: { ser: number } }) {
  if (isBlank(params.ser)) {
    return <Page5xx></Page5xx>;
  } else {
    const settleResponse = await getPointSettle(params.ser);
    if (settleResponse.status === 200) {
      const userResponse = await getUser(settleResponse.pointSettle.owner);

      return (
        <>
          <div className="container-fixed">
            <div className="flex flex-col">
              <div className="flex flex-wrap items-center justify-between gap-5 pb-5 lg:items-end">
                <div className="flex flex-nowrap items-center justify-center gap-2">
                  <Link href={`/admin/point/settle/list`} className="flex flex-nowrap items-center hover:text-primary">
                    <IoIosArrowBack size={'1.3em'} />
                  </Link>
                  <h1 className="text-xl font-semibold leading-none text-gray-900">정산 세부 정보</h1>
                </div>
              </div>

              <div className="flex flex-col gap-5">
                {userResponse.status == 200 && <UserProfileComponent user={userResponse.user}></UserProfileComponent>}
              </div>
            </div>
          </div>
          <UserSettleListComponent owner={settleResponse.pointSettle.owner}></UserSettleListComponent>
          <SettleInfoComponent settle={settleResponse.pointSettle}></SettleInfoComponent>
        </>
      );
    }
  }
}
