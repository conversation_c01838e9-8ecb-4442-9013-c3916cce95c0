'use client';
import { registerFaq } from '@/action/faq-action';
import AlertModal from '@/components/modal/alert-modal';
import { CategoryCode } from '@/types/category-code';
import { Faq } from '@/types/faq';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { ChangeEvent, FocusEvent, useEffect, useState } from 'react';
import { z } from 'zod';
interface PageProps {
  maxOrder: number;
  categoryCodeListData: CategoryCode[];
}
export default function FaqRegsiterComponent({ maxOrder, categoryCodeListData }: PageProps) {
  const router = useRouter();
  const [errors, setErrors] = useState<FaqError>({});
  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();

  const [faq, setFaq] = useState<Faq>({
    ser: 0,
    category: '',
    categoryCode: '',
    questions: '',
    answers: '',
    locale: 'ko',
    rowNum: 0,
    createdAt: null,
    lastSeen: null
  });

  const schema = z.object({
    categoryCode: z.string().min(1, '구분을 입력하세요'),
    locale: z.string().refine((value) => value === 'en' || value === 'ko', {
      message: '지역을 선택하세요.'
    }),
    rowNum: z.number().refine((value) => value > 0 && value <= 100, {
      message: '순서를 선택하세요'
    }),
    questions: z.string().min(1, {
      message: '질문을 입력하세요.'
    }),
    answers: z.string().min(1, {
      message: '답변을 입력하세요.'
    })
  });

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: Faq, field?: keyof Faq): FaqError => {
    try {
      schema.parse(data);
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;
        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);

        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };

  useEffect(() => {
    const init = async () => {
      setErrors({});
    };
    init();
  }, []);

  /**
   * @brief INPUT, TEXTAREA, SELECT 값변경
   * @param e
   */
  const onChangeHandler = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, tagName } = e.currentTarget || e.target;
    if (name === 'rowNum') {
      const updateFaq = { ...faq, [name]: Number(value) };
      setFaq(updateFaq);
      validateForm(updateFaq, name as keyof FaqType);
    } else if (name === 'categoryCode') {
      const category = categoryCodeListData.find((item) => item.code === value);
      const updateFaq = { ...faq, category: category.type, categoryCode: category.code };
      setFaq(updateFaq);
    } else {
      if (type === 'number') {
        const updateFaq = { ...faq, [name]: Number(value) };
        setFaq(updateFaq);
      } else {
        const updateFaq = { ...faq, [name]: value };
        setFaq(updateFaq);
      }
    }
  };

  /**
   * @brief INPUT, TEXTAREA Validate
   * @param e
   */
  const onBlurHandler = (e: FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.currentTarget || e.target;

    const updateFormData = { ...faq, [name]: type == 'number' ? Number(value) : value };

    validateForm(updateFormData, name as keyof FaqType);
  };

  /**
   * @brief 정렬순서 옵션 생성
   * @param order
   * @returns
   */
  const viewOrderOptions = (order: number) => {
    let options = [];
    for (let i = 1; i <= order; i++) {
      options.push(
        <option value={i} key={'rowNum_key_' + i}>
          {i}
        </option>
      );
    }
    return options;
    return;
  };

  /**
   * @brief FAQ 등록
   */
  const register = async () => {
    const newErrors = validateForm(faq);
    if (Object.keys(newErrors).length === 0) {
      const { createdAt, lastSeen, ...updateForm } = faq;

      const response = await registerFaq(updateForm);

      if (response.status === 200) {
        // window.location.href = `/admin/site/faq?locale=${faq.locale}`;
        router.push(`/admin/site/faq?locale=${faq.locale}`);
      } else {
        setAlertModalData({
          btnColor: 'btn-primary',
          title: '경고',
          content: 'FAQ 등록 실패',
          okBtn: '확인'
        });

        setAlertModal(true);
      }
    }
  };

  return (
    <>
      <div className="flex flex-col">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card min-w-full">
            {/* <div className="card-header">
              <h3 className="card-title"></h3>
            </div> */}
            <div className="card-body lg:py-7.5">
              <div className="flex flex-col gap-6">
                {/* <div className="grid grid-cols-1 gap-3 lg:grid-cols-3"> */}
                <div className="flex gap-10">
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-20 text-2sm text-gray-600">구분</span>
                    <div className="flex flex-col gap-1.5">
                      {/* <input
                        className="input text-2sm font-semibold text-gray-900"
                        name="category"
                        id="category"
                        type="text"
                        value={faq.category}
                        onChange={onChangeHandler}
                        onBlur={onBlurHandler}
                      /> */}

                      <div className="relative w-auto">
                        <select
                          className="select select-sm mr-4"
                          name="categoryCode"
                          value={faq?.categoryCode || ''}
                          onChange={onChangeHandler}
                        >
                          <option value="" key="categoryCode_a">
                            선택
                          </option>
                          {categoryCodeListData.length > 0 &&
                            categoryCodeListData.map((item, index) => (
                              <option value={item.code} key={`categoryCode_${index}`}>
                                {item.category}
                              </option>
                            ))}
                        </select>
                      </div>

                      {errors.categoryCode && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.categoryCode}</span>
                      )}
                    </div>
                  </div>
                  <div className="mx-1 border-r border-gray-200"></div>
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-20 text-2sm text-gray-600">지역</span>
                    {/* <input className="input text-2sm font-semibold text-gray-900" name="locale" value={faqData.locale} /> */}
                    <select className="select !w-[80px]" name="locale" value={faq.locale} onChange={onChangeHandler}>
                      <option value="ko">ko</option>
                      <option value="en">en</option>
                    </select>
                  </div>
                  <div className="mx-1 border-r border-gray-200"></div>
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-20 text-2sm text-gray-600">순서</span>
                    <div className="flex flex-col gap-1.5">
                      <select className="select !w-[90px]" name="rowNum" value={faq.rowNum} onChange={onChangeHandler}>
                        <option key="rowNum_key_a">- 선택 -</option>
                        {viewOrderOptions(maxOrder)}
                      </select>
                      {errors.rowNum && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.rowNum}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="my-1 border-t border-gray-200"></div>

                <div className="flex items-center gap-1.5 max-md:w-full">
                  <span className="text-medium min-w-20 text-2sm text-gray-600">질문</span>
                  <div className="flex grow flex-col gap-1.5">
                    <input
                      className="input text-2sm font-semibold text-gray-900"
                      name="questions"
                      value={faq.questions}
                      onChange={onChangeHandler}
                      onBlur={onBlurHandler}
                    />
                    {errors.questions && (
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.questions}</span>
                    )}
                  </div>
                </div>
                <div className="my-1 border-t border-gray-200"></div>
                <div className="flex items-start gap-1.5 pb-7 max-md:w-full">
                  <span className="text-medium min-w-20 text-2sm text-gray-600">답변</span>
                  <div className="flex grow flex-col gap-1.5">
                    <textarea
                      className="textarea w-full !bg-white text-[13px] !text-[#181C32]"
                      rows={20}
                      name="answers"
                      value={faq.answers}
                      onChange={onChangeHandler}
                      onBlur={onBlurHandler}
                    ></textarea>
                    {errors.answers && (
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.answers}</span>
                    )}
                  </div>
                </div>
              </div>
              <div className="card-footer">
                <div className="flex grow justify-end gap-3">
                  <button
                    className="btn btn-sm btn-secondary"
                    onClick={(e) => {
                      e.preventDefault();
                      router.back();
                    }}
                  >
                    취소
                  </button>
                  <button className="btn btn-sm btn-primary" onClick={register}>
                    등록
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {alertModal && (
        <AlertModal
          alertModal={alertModal}
          alertModalData={alertModalData}
          onCloseModal={() => {
            setAlertModal(false);
          }}
        ></AlertModal>
      )}
    </>
  );
}
