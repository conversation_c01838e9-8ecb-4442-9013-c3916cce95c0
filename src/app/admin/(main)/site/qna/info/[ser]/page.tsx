import { getCredentails, getUser, getUsers } from '@/action/user-action';
import Page5xx from '@/components/page_5xx';
import { isBlank } from '@/utils';

import { getQna } from '@/action/qna-action';
import UserProfilePage from '@/app/admin/(main)/account/user/info/[email]/user-profile';
import { User, UserRequest, UserResponse } from '@/types/user';
import QnaClusterState from './cluster-state';
import QnaInfo from './qna-info';
import { getNodes } from '@/action/node-action';
import { NodeRequest, NodeResponse } from '@/types/node';
import { getWorkloads } from '@/action/workload-action';
import { WorkloadRequest, WorkloadResponse } from '@/types/workload';
import { CategoryCode, CategoryCodeRequest, CategoryCodeResponse } from '@/types/category-code';
import { getCategoryCodes } from '@/action/category-action';

export default async function QnAInfoPage({ params }: { params: { ser: number } }) {
  if (isBlank(params.ser)) {
    return <Page5xx></Page5xx>;
  } else {
    const qnaResponse = await getQna(params.ser);

    if (qnaResponse.status !== 200) return <Page5xx></Page5xx>;

    const owner = qnaResponse.qna.owner;

    const userResponse: UserResponse = await getUser(owner);

    let categoryCodeList: CategoryCode[] = [];
    let managerList: User[] = [];

    // 카테고리 코드 요청
    const categoryCodeRequest: CategoryCodeRequest = { startNum: 0, scaleNum: 0, type: 'QNA', isUsed: true };
    const categoryCodeResponse: CategoryCodeResponse = await getCategoryCodes(categoryCodeRequest);

    if (categoryCodeResponse.status === 200) {
      categoryCodeList = categoryCodeResponse.codes;
    }

    // 관리자 정보 요청
    let managerRequest: UserRequest = {
      startNum: 0,
      scaleNum: 0,
      name: '',
      email: '',
      startTime: '',
      endTime: '',
      namespace: '',
      role: 'ROLE_ADMIN',
      sortName: 'email',
      sortType: 'asc'
    };
    const managerResponse: UserResponse = await getUsers(managerRequest);
    if (managerResponse.status === 200) {
      managerList = managerResponse.users;
    }

    return (
      <>
        <div className="container-fixed">
          <div className="flex flex-col gap-5">
            <div className="flex flex-wrap items-center justify-between gap-5 pb-7.5 lg:items-end">
              <div className="flex flex-col justify-center gap-2">
                <h1 className="text-xl font-semibold leading-none text-gray-900">QnA 상세보기</h1>
              </div>
            </div>

            <div className="flex flex-col gap-5">
              {userResponse.status == 200 && <UserProfilePage user={userResponse.user}></UserProfilePage>}
            </div>

            <div className="grid grid-cols-[400px_1fr] gap-5">
              {qnaResponse.status === 200 && (
                <>
                  <QnaClusterState owner={owner}></QnaClusterState>
                  <QnaInfo qnaData={qnaResponse.qna} managerListData={managerList} categoryCodeListData={categoryCodeList} />
                </>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }
}
