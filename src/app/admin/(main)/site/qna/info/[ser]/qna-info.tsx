'use client';
import { updateQnaAnswer, updateQnaCategory, updateQnaManager } from '@/action/qna-action';
import { CategoryCode } from '@/types/category-code';
import { Qna } from '@/types/qna';
import { User } from '@/types/user';
import Link from 'next/link';
import { ChangeEvent, useEffect, useState } from 'react';
import useNotification from 'raxeraditya-toast';
import { dateTime, isBlank } from '@/utils';
import { z } from 'zod';
import AlertModal from '@/components/modal/alert-modal';

interface PageProps {
  qnaData: Qna;
  managerListData: User[];
  categoryCodeListData: CategoryCode[];
}

export default function QnaInfo({ qnaData, managerListData, categoryCodeListData }: PageProps) {
  const { NotificationComponent, triggerNotification } = useNotification('top-right');
  const [errors, setErrors] = useState<QnaError>({});

  const [qna, setQna] = useState<Qna | null>(null);

  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();

  const schema = z.object({
    manager: z.preprocess((val) => val ?? '', z.string().min(1, '담당자를 지정해주세요.')),
    answer: z.preprocess((val) => val ?? '', z.string().min(1, '답변을 입력하세요.'))
  });

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: Qna, field?: keyof QnaType): QnaError => {
    try {
      schema.parse(data);
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;
        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);

        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };

  useEffect(() => {
    const init = async () => {
      setQna(qnaData);
    };
    init();
  }, []);

  /**
   * @brief 내용 변환
   * @param value
   * @returns
   */
  const convertContent = (value: string) => {
    return value?.replaceAll('\n', '<br/>');
  };

  /**
   * @brief INPUT 값 변경
   * @param e
   */
  const onInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setQna((prevData) => (prevData ? { ...prevData, [name]: value } : null));
  };

  /**
   * @brief SELECT 값 변경
   * @param e
   */
  const onSelectChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setQna((prevData) => (prevData ? { ...prevData, [name]: value } : null));
  };

  /**
   * @brief 문의유형 변경
   */
  const changeCategoryCode = async () => {
    const response = await updateQnaCategory(qna);

    if (response.status === 200) {
      triggerNotification({
        type: 'success',
        message: `문의사항 변경 완료`,
        duration: 3000,
        onClose: () => {}
      });

      setQna((prevData) => ({ ...prevData, categoryCode: response.qna.categoryCode }));
    } else {
      triggerNotification({
        type: 'error',
        message: `문의사항 변경 실패`,
        duration: 3000,
        onClose: () => {}
      });
      setQna((prevData) => ({ ...prevData, categoryCode: qnaData.categoryCode }));
    }
  };

  /**
   * @brief 담당자 변경
   */
  const changeManager = async () => {
    const newErrors = validateForm(qna, 'manager' as keyof QnaType);
    if (newErrors.manager === null) {
      const response = await updateQnaManager(qna);
      if (response.status === 200) {
        triggerNotification({
          type: 'success',
          message: `담당자 변경완료`,
          duration: 3000,
          onClose: () => {}
        });

        setQna((prevData) => ({ ...prevData, manager: response.qna.manager, status: response.qna.status }));
      } else {
        triggerNotification({
          type: 'error',
          message: `담당자 변경 실패`,
          duration: 3000,
          onClose: () => {}
        });
        setQna((prevData) => ({ ...prevData, manager: qnaData.manager }));
      }
    }
  };

  /**
   * @brief 답변 등록
   */
  const onSubmit = async () => {
    const newErrors = validateForm(qna);

    if (Object.keys(newErrors).length === 0) {
      const response = await updateQnaAnswer(qna);

      if (response.status === 200) {
        triggerNotification({
          type: 'success',
          message: `답변등록 완료`,
          duration: 3000,
          onClose: () => {}
        });
        setQna(response.qna);
      } else {
        triggerNotification({
          type: 'error',
          message: `답변등록 실패`,
          duration: 3000,
          onClose: () => {}
        });
        setQna((prevData) => ({ ...prevData, manager: qnaData.manager }));
      }
    } else {
      if (newErrors.manager) {
        setAlertModalData({
          btnColor: 'btn-primary',
          title: '경고',
          content: '담당자를 지정해주세요.',
          okBtn: '확인'
        });
      }

      setAlertModal(true);
    }
  };

  /**
   * @brief 담당자 변환
   * @param email
   * @returns
   */
  const convertManager = (email: string) => {
    const item = managerListData.find((item) => item.email === email);
    return item ? `${item.name} - ${item.email}` : '';
  };

  // const convertCategory = (categoryCode: string) => {
  //   const item = categoryCodeListData.find((item) => item.code === categoryCode);
  //   return item ? `${item.type} - ${item.code}` : '';
  // };

  if (qna == null) return <div></div>;
  return (
    <>
      <div className="flex flex-col">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card min-w-full">
            <div className="card-header">
              <h3 className="card-title">QnA 정보</h3>
            </div>
            <div className="card-body">
              <div className="flex flex-col gap-6">
                <div className="grid grid-cols-2">
                  <div className="flex w-full items-center">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">문의유형</div>
                    {qna?.status !== 'answered' && (
                      <div className="flex items-center gap-2">
                        <div className="relative w-auto">
                          <select
                            className="select select-sm mr-4"
                            name="categoryCode"
                            value={qna?.categoryCode || ''}
                            onChange={onSelectChange}
                          >
                            <option value="" key="categoryCode_a">
                              선택
                            </option>
                            {categoryCodeListData.length > 0 &&
                              categoryCodeListData.map((item, index) => (
                                <option value={item.code} key={`categoryCode_${index}`}>
                                  {/* {`${item.type} - ${item.code}`} */}
                                  {item.category}
                                </option>
                              ))}
                          </select>
                        </div>

                        <button
                          className="btn btn-sm btn-primary text-nowrap"
                          onClick={() => {
                            changeCategoryCode();
                          }}
                        >
                          변경
                        </button>
                      </div>
                    )}
                    {qna?.status === 'answered' && <div className="text-2sm font-semibold text-gray-900">{qna?.category || ''}</div>}
                  </div>

                  <div className="flex justify-end">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">등록일</div>
                    <span className="text-2sm font-semibold text-gray-900">{dateTime(qna?.createdAt)}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="flex">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">이름</div>
                    <span className="text-2sm font-semibold text-gray-900">{qna?.name || ''}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="flex">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">제목</div>
                    <span className="text-2sm font-semibold text-gray-900">{qna?.title || ''}</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="flex">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">대상문의</div>
                    <span className="text-2sm font-semibold text-gray-900">
                      {qna?.targetType === 'workload' ? '워크로드 - ' : qna?.targetType === 'node' ? '노드 - ' : ''}
                      {!isBlank(qna?.target) && qna?.targetType === 'workload' && (
                        <Link className="link" target="_blank" href={`/admin/workload/detail/${qna?.target}`}>
                          {qna?.target}
                        </Link>
                      )}
                      {!isBlank(qna?.target) && qna?.targetType === 'node' && (
                        <Link className="link" target="_blank" href={`/admin/node/detail/${qna?.target}`}>
                          {qna?.target}
                        </Link>
                      )}
                    </span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="flex w-full">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">내용</div>
                    <span
                      className="w-full rounded-md border p-2 text-2sm font-semibold text-gray-900"
                      dangerouslySetInnerHTML={{ __html: convertContent(qna?.content || '') }}
                    ></span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="flex w-full items-center">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">담당자</div>
                    <div className="flex items-center gap-2">
                      <div className="relative w-auto">
                        {qna?.status === 'answered' && (
                          <div className="text-2sm font-semibold text-gray-900">{convertManager(qna?.manager || '')}</div>
                        )}
                        {qna?.status !== 'answered' && (
                          <select className="select select-sm mr-4" name="manager" value={qna?.manager || ''} onChange={onSelectChange}>
                            <option value="">선택</option>
                            {managerListData.length > 0 &&
                              managerListData.map((item, index) => (
                                <option value={item.email} key={`manager_${index}`}>
                                  {`${item.name} - ${item.email}`}
                                </option>
                              ))}
                          </select>
                        )}
                      </div>
                      {qna?.status !== 'answered' && (
                        <button
                          className="btn btn-sm btn-primary text-nowrap"
                          onClick={() => {
                            changeManager();
                          }}
                        >
                          저장
                        </button>
                      )}
                      {errors.manager && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.manager}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap gap-5 md:flex-nowrap">
                  <div className="flex w-full">
                    <div className="min-w-24 pt-2 text-2sm font-medium text-gray-600">답변</div>
                    <div className="flex w-full flex-col gap-1.5">
                      {qna?.status !== 'answered' && (
                        <textarea
                          className="textarea w-full"
                          name="answer"
                          rows={10}
                          // disabled={qna?.status !== 'processing'}
                          value={qna?.answer || ''}
                          onChange={onInputChange}
                        ></textarea>
                      )}
                      {qna?.status === 'answered' && (
                        <span
                          className="max-h-[250px] min-h-[250px] w-full overflow-y-auto rounded-md border p-2 text-2sm font-semibold text-gray-900"
                          dangerouslySetInnerHTML={{ __html: convertContent(qna?.answer || '') }}
                        ></span>
                      )}
                      {errors.answer && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.answer}</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2">
                  <div className="flex w-full">
                    <div className="min-w-24 text-2sm font-medium text-gray-600">상태</div>
                    <div className="flex gap-2">
                      <label className="form-label flex items-center gap-2.5 text-nowrap">
                        <input
                          type="radio"
                          className="radio radio-sm"
                          name="status"
                          defaultValue="registered"
                          readOnly
                          checked={qna.status === 'registered'}
                          disabled={qna.status !== 'registered'}
                        />
                        등록완료
                      </label>
                      <label className="form-label flex items-center gap-2.5 text-nowrap">
                        <input
                          type="radio"
                          className="radio radio-sm"
                          name="status"
                          defaultValue="received"
                          readOnly
                          checked={qna.status === 'received'}
                          disabled={qna.status !== 'received'}
                        />
                        접수완료
                      </label>
                      <label className="form-label flex items-center gap-2.5 text-nowrap">
                        <input
                          type="radio"
                          className="radio radio-sm"
                          name="status"
                          defaultValue="processing"
                          readOnly
                          checked={qna.status === 'processing'}
                          disabled={qna.status !== 'processing'}
                        />
                        처리중
                      </label>
                      <label className="form-label flex items-center gap-2.5 text-nowrap">
                        <input
                          type="radio"
                          className="radio radio-sm"
                          name="status"
                          defaultValue="answered"
                          readOnly
                          checked={qna.status === 'answered'}
                          disabled={qna.status !== 'answered'}
                        />
                        답변완료
                      </label>
                    </div>
                  </div>
                  {qna?.answerAt !== null && (
                    <div className="flex w-full justify-end">
                      <div className="min-w-24 text-2sm font-medium text-gray-600">답변일</div>

                      <span className="text-2sm font-semibold text-gray-900">{dateTime(qna?.answerAt)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="card-footer">
              <div className="flex w-full justify-end gap-4">
                <Link href={`/admin/site/qna`} className="btn btn-light">
                  {qna?.status === 'answered' ? '목록' : '취소'}
                </Link>
                {qna?.status !== 'answered' && (
                  <button className="btn btn-primary" onClick={onSubmit}>
                    답변등록
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {NotificationComponent}
        {alertModal && (
          <AlertModal
            alertModal={alertModal}
            alertModalData={alertModalData}
            onCloseModal={() => {
              setAlertModal(false);
            }}
          ></AlertModal>
        )}
      </div>
    </>
  );
}
