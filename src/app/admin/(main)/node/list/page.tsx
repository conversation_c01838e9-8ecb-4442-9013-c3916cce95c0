import { getNodes } from '@/action/node-action';
import { Node, NodeRequest, NodeResponse } from '@/types/node';
import { Paging } from '@/types/paging';
import NodeTable from './node-table';

type PageProps = {
  searchParams: { [key: string]: string | string[] | undefined };
};

export default async function NodeListPage({ searchParams }: PageProps) {
  let nodeRequest: NodeRequest = {
    startNum: 0,
    scaleNum: 15,
    owner: '',
    name: '',
    category: '',
    cloud: '',
    gpuSpec: '',
    hostSpec: '',
    state: '',
    isDedicated: '',
    dedicatedNmsp: '',
    startTime: '',
    endTime: '',
    sortName: 'created_at',
    sortType: 'desc'
  };
  let nodeList: Node[] = [];
  let paging: Paging | null = null;

  const search = async () => {
    for (const key in searchParams) {
      if (searchParams[key] !== undefined && key in nodeRequest) {
        const value = searchParams[key];
        if (key === 'startNum' || key === 'scaleNum') {
          nodeRequest[key] = Number(value);
        } else if (
          key === 'owner' ||
          key === 'name' ||
          key === 'category' ||
          key === 'cloud' ||
          key === 'gpuSpec' ||
          key === 'hostSpec' ||
          key === 'state' ||
          key === 'isDedicated' ||
          key === 'startTime' ||
          key === 'endTime' ||
          key === 'sortName' ||
          key === 'sortType'
        ) {
          nodeRequest[key] = value as string;
        }
      }
    }
    const nodeResponse: NodeResponse = await getNodes(nodeRequest);
    if (nodeResponse.status == 200) {
      nodeList = nodeResponse.nodes;
      paging = nodeResponse.paging as Paging;
    }
  };

  await search();

  return (
    <>
      <div className="container-fixed">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-7.5 lg:items-end">
          <div className="flex flex-col justify-center gap-2">
            <h1 className="text-xl font-semibold leading-none text-gray-900">노드 목록</h1>
          </div>
        </div>

        <NodeTable nodeList={nodeList} paging={paging} nodeRequest={nodeRequest}></NodeTable>
      </div>
    </>
  );
}
