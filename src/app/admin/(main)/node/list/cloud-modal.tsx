'use client';

import { KTModal } from '@/metronic/core';
import React, { useEffect, ReactNode, ChangeEvent, useState } from 'react';
import ReactDOM from 'react-dom';
import JSONInput from 'react-json-editor-ajrm';
import en from 'react-json-editor-ajrm/locale/en';

interface ModalProps {
  title: string;
  data?: any;
  onModalHandler: (data: any) => void;
}

interface PageProps {
  cloudModal: boolean;
  cloudModalData: ModalProps;
  onCloseModal: () => void;
}

/**
 * @brief Cloud Modal Component
 * @param param
 * @returns
 */
export default function CloudModal({ cloudModal, cloudModalData, onCloseModal }: PageProps) {
  const [name, SetName] = useState<any>('');
  const [vmParams, SetVmParams] = useState<any>({});
  // cloudModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    SetName(cloudModalData.data.name);
    SetVmParams(cloudModalData.data.vmParams);
    if (cloudModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [cloudModal]);

  /**
   * @brief Modal 열기 및 숨겨질 때 이벤트 등록
   */
  const onOpen = () => {
    const cloudModalEl = document.querySelector('#cloudModal') as HTMLElement;
    const cloudModalInt = KTModal.getInstance(cloudModalEl);
    cloudModalInt.on('hidden', () => {
      onCloseModal();
    });
    cloudModalInt.show();
  };

  /**
   * @brief Modal 닫기
   */
  const onClose = () => {
    const cloudModalEl = document.querySelector('#cloudModal') as HTMLElement;
    const cloudModalInt = KTModal.getInstance(cloudModalEl);
    cloudModalInt.hide();
  };

  /**
   * @brief JSON Editor에서 변경한 데이터 담기
   * @param e
   */
  const onChangeVmParams = (e: any) => {
    if (!e.error) {
      SetVmParams(e.jsObject);
    }
  };

  /**
   * @brief 변경 버튼 클릭 시, 팝업 요청 컴포넌트로 데이터 전달 및 Modal 닫기
   */
  const onSubmit = () => {
    cloudModalData.onModalHandler({ name: name, vmParams: vmParams });
    onClose();
  };

  if (!cloudModal) return null;
  return ReactDOM.createPortal(
    <>
      <div className="modal" data-modal="true" id="cloudModal">
        <div className="modal-content top-[10%] max-w-[600px]">
          <div className="modal-header">
            <h3 className="modal-title">{cloudModalData?.title}</h3>
            <button className="btn-icon btn btn-xs btn-light" type="button" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <JSONInput
              placeholder={vmParams}
              onChange={onChangeVmParams}
              height="300px"
              width="100%"
              locale={en}
              style={{ body: { fontSize: '14px' } }}
            ></JSONInput>
          </div>
          <div className="modal-footer justify-end">
            <div className="flex gap-4">
              <button className="btn btn-light" onClick={() => onClose()}>
                취소
              </button>
              <button
                type="button"
                className={`btn btn-primary`}
                onClick={() => {
                  onSubmit();
                }}
              >
                설정
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,
    document.getElementById('global_modal')
  );
}
