'use client';
import Paginate from '@/components/paging/paginate';
import AlertModal from '@/components/modal/alert-modal';
import ConfirmModal from '@/components/modal/confirm-modal';
import CloudModal from '@/app/admin/(main)/node/list/cloud-modal';
import DedicatedModal from '@/app/admin/(main)/node/list/dedicated-modal';
import { deleteNode, getSearchNode, updateNodeVmParams, updateDedicatedNode, cancelDedicatedNode } from '@/action/node-action';
import { Paging } from '@/types/paging';
import { Node, NodeRequest, NodeResponse } from '@/types/node';
import { PointBase } from '@/types/point';
import { generateUrlSearchParams, isBlank, dateTime, comma, emailId } from '@/utils';
import { tier, spec, state } from '@/utils/node-utils';
import { usePathname, useRouter } from 'next/navigation';
import { ChangeEvent, KeyboardEvent, MouseEvent, useCallback, useEffect, useRef, useState } from 'react';
import { DatePicker } from '@mui/x-date-pickers';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import Link from 'next/link';
import { KTDropdown } from '@/metronic/core';
import { getSearchUser } from '@/action/user-action';
import { User, UserResponse } from '@/types/user';
import { debounce, throttle } from 'lodash-es';
import NodeTaintState from '@/components/node/node-taint-state';

interface PageProps {
  nodeList: Node[];
  paging: Paging;
  nodeRequest: NodeRequest;
}
export default function NodeTable({ nodeList, paging, nodeRequest }: PageProps) {
  const router = useRouter();
  const pathName = usePathname();
  const [nodesData, setNodesData] = useState<Node[]>([]);
  const [pagingData, setPageData] = useState<Paging | null>(null);

  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();
  const [confirmModal, setConfirmModal] = useState<boolean>(false);
  const [confirmModalData, setConfirmModalData] = useState<any>();
  const [cloudModal, setCloudModal] = useState<boolean>(false);
  const [cloudModalData, setCloudModalData] = useState<any>();
  const [dedicatedModal, setDedicatedModal] = useState<boolean>(false);
  const [dedicatedModalData, setDedicatedModalData] = useState<any>();

  const [nodeRequestData, setNodeRequestData] = useState<NodeRequest>({
    startNum: 0,
    scaleNum: 15,
    owner: '',
    name: '',
    category: '',
    cloud: '',
    gpuSpec: '',
    hostSpec: '',
    state: '',
    isDedicated: '',
    dedicatedNmsp: '',
    startTime: '',
    endTime: '',
    sortName: '',
    sortType: ''
  });

  const [sortRequest, setSortRequest] = useState<any | null>(null);

  const emailDropDownRef = useRef<any>(null);
  const nodeDropDownRef = useRef<any>(null);

  const [emailSearchList, setEmailSearchList] = useState<User[]>([]);
  const [nodeSearchList, setNodeSearchList] = useState<string[]>([]);

  useEffect(() => {
    const init = async () => {
      setNodesData(nodeList);
      setPageData(paging);
      setNodeRequestData(nodeRequest);
      initDropdown();
      tableSort();
    };

    const initDropdown = async () => {
      const emailDropdownEl = document.querySelector('#email_dropdown') as HTMLElement;
      emailDropDownRef.current = KTDropdown.getInstance(emailDropdownEl);

      const nodeDropdownEl = document.querySelector('#node_dropdown') as HTMLElement;
      nodeDropDownRef.current = KTDropdown.getInstance(nodeDropdownEl);
      if (nodeRequest?.owner) {
        searchIntelliSense('owner', nodeRequest?.owner, false);
      }

      if (nodeRequest?.name) {
        searchIntelliSense('name', nodeRequest?.name, false);
      }
    };

    const tableSort = async () => {
      const sortTagList = document.querySelectorAll('.d-sort');
      sortTagList.forEach((item) => {
        if (item.getAttribute('data-sort-column') === nodeRequest.sortName) {
          item.className = `d-sort ${nodeRequest.sortType.toLowerCase()} r`;
        }

        item.addEventListener('click', (e) => {
          const orderName = item.getAttribute('data-sort-column');

          const orderType = item.classList.contains('asc') ? 'desc' : 'asc';

          document.querySelectorAll('.d-sort').forEach((el) => {
            el.className = 'd-sort';
          });

          item.className = `d-sort ${orderType}`;

          setNodeRequestData((prevData) => ({ ...prevData, sortName: orderName, sortType: orderType }));
          setSortRequest({ sortName: orderName, sortType: orderType });
        });
      });
    };
    init();

    return () => {
      const sortTagList = document.querySelectorAll('.d-sort');
      sortTagList.forEach((item) => {
        item.removeEventListener('click', onSortSearch);
      });
    };
  }, []);

  useEffect(() => {
    if (sortRequest != null) {
      onSortSearch();
    }
  }, [sortRequest]);

  useEffect(() => {
    const sortTagList = document.querySelectorAll('.d-sort');
    sortTagList.forEach((item) => {
      if (item.getAttribute('data-sort-column') === nodeRequestData.sortName) {
        item.className = `d-sort ${nodeRequestData.sortType.toLowerCase()} r`;
      } else {
        item.className = `d-sort `;
      }
    });
  }, [nodeRequestData.sortName, nodeRequestData.sortType]);

  /**
   * @brief INPUT 값 변경
   * @param e
   */
  const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setNodeRequestData((prevData) => ({ ...prevData, [name]: value }));

    //사용자 이메일/이름 검색 후 자동완성 기능
    debounceSearchIntelliSense(name, value);
  };

  const onSelectChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setNodeRequestData((prevData) => ({ ...prevData, [name]: value }));
  };

  const onSearch = async (startNum: number) => {
    const requestData = { ...nodeRequestData };
    requestData.startNum = startNum;
    requestData.sortName = 'created_at';
    requestData.sortType = 'desc';
    setNodeRequestData((prevData) => ({ ...prevData, startNum: startNum }));
    const queryStr = generateUrlSearchParams(requestData);

    window.location.href = `${pathName}?${queryStr}`;
  };

  /**
   * @brief 정렬 검색
   */
  const onSortSearch = async () => {
    const requestData = { ...nodeRequestData };
    requestData.startNum = 0;

    setNodeRequestData(requestData);

    const queryStr = generateUrlSearchParams(requestData);

    window.location.href = `${pathName}?${queryStr}`;
  };

  /**
   * @brief Input 인텔리센스 지연 실행
   */
  const debounceSearchIntelliSense = useCallback(
    debounce(
      (name: string, value: string) => {
        searchIntelliSense(name, value);
      },
      200,
      { leading: false, trailing: true }
    ),
    []
  );
  /**
   * @brief Input 인텔리센스
   * @param name
   * @param value
   */
  const searchIntelliSense = (name: string, value: string, display: boolean = true) => {
    const type = name === 'owner' || name === 'name' ? 'email' : name;
    if (name == 'owner') {
      getSearchUser(type, value)
        .then((response: UserResponse) => {
          if (response.status === 200) {
            if (name === 'owner') {
              setEmailSearchList(response.users);
              display ? emailDropDownRef?.current.show() : emailDropDownRef?.current.hide();
            }
          } else {
            if (name === 'owner') {
              setEmailSearchList([]);
              display ? emailDropDownRef?.current.show() : emailDropDownRef?.current.hide();
            }
          }
        })
        .catch((err: any) => {
          console.log(err);
        });
    } else if (name === 'name') {
      getSearchNode(value)
        .then((response: any) => {
          if (response.status === 200) {
            if (name === 'name') {
              setNodeSearchList(response.nodes as string[]);
              display ? nodeDropDownRef?.current.show() : nodeDropDownRef?.current.hide();
            }
          } else {
            if (name === 'name') {
              setNodeSearchList([]);
              display ? nodeDropDownRef?.current.show() : nodeDropDownRef?.current.hide();
            }
          }
        })
        .catch((err: any) => {
          console.log(err);
        });
    }
  };

  /**
   * @brief 사용자 선택
   * @param name
   * @param value
   */
  const selectUser = (name: string, value: string) => {
    setNodeRequestData((prevData) => ({ ...prevData, [name]: value }));
    if (name === 'owner') {
      const target = emailSearchList.filter((item) => item.email === value);
      setEmailSearchList(target);
      emailDropDownRef.current.hide();
    } else if (name === 'name') {
      const target = nodeSearchList.filter((item) => item === value);
      setNodeSearchList(target);
      nodeDropDownRef.current.hide();
    }
  };

  const onInputEnter = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSearch(0);
    }
  };

  /**
   * @brief DateTimePicker 변경
   * @param name
   * @param value
   */
  const changeDataTime = (name: string, value: Dayjs) => {
    setNodeRequestData((prevData) => ({ ...prevData, [name]: dayjs(value).format('YYYY-MM-DD') }));
  };

  const openConfirmModal = (index: number) => {
    const item = nodeList[index];
    setConfirmModalData({
      btnColor: 'btn-danger',
      title: '노드 삭제',
      htmlContent: `<div class='flex flex-col gap-4 text-sm'>
                      <p >${item.name}을 삭제하시겠습니까?</p>
                    </div>`,
      okBtn: '삭제',
      data: { name: item.name },
      onConfirmHandler: onConfirmHandler
    });
    setConfirmModal(true);
  };

  const onConfirmHandler = async (data: Node) => {
    const response = await deleteNode(data.name);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: '경고',
        content: '노드 삭제 실패',
        okBtn: '확인'
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };

  /**
   * @brief Cloud Edit Modal
   */
  const openCloudModal = (index: number) => {
    const item = nodeList[index];
    let data = {};
    if (isBlank(item.vmParams)) {
      const jsonStr = '{ "vpcId" : "", "subnetId" : "", "acgId" : "", "svrImgId" : "", "initScrId" : "", "loginKeyId" : "" }';
      data = {
        name: item.name,
        vmParams: JSON.parse(jsonStr)
      };
    } else {
      data = {
        name: item.name,
        vmParams: JSON.parse(item.vmParams)
      };
    }

    setCloudModalData({
      title: '네이버 클라우드 VM 파라미터',
      data: data,
      onModalHandler: onCloudModalHandler
    });

    setCloudModal(true);
  };

  const onCloudModalHandler = async (data: any) => {
    const body = {
      name: data.name,
      vmParams: JSON.stringify(data.vmParams)
    };
    const response = await updateNodeVmParams(body);
    setCloudModal(false);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: '경고',
        content: 'VM 파라미터 설정 실패',
        okBtn: '확인'
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };

  /**
   * @brief Dedicated Modal
   */
  const openDedicatedModal = (index: number) => {
    const item = nodeList[index];
    let data = {
      nodeName: item.name
    };
    setDedicatedModalData({
      title: '노드지정',
      data: data,
      onModalHandler: onDedicatedModalHandler
    });

    setDedicatedModal(true);
  };

  const onDedicatedModalHandler = async (data: any) => {
    console.log('onDedicatedModalHandler', data);
    const response = await updateDedicatedNode(data);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: '경고',
        content: '노드 지정 실패',
        okBtn: '확인'
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
    setDedicatedModal(false);
  };
  /**
   * @brief Dedicated Cancel
   */
  const cancelDedicated = (index: number) => {
    const item = nodeList[index];
    setConfirmModalData({
      btnColor: 'btn-danger',
      title: '노드 지정 해제',
      htmlContent: `<div class='flex flex-col gap-4 text-sm'>
                      <p >${item.name}에 지정된 사용자 ${item.dedicatedNmsp}를 해제 하시겠습니까?</p>
                    </div>`,
      okBtn: '해제',
      data: { nodeName: item.name, namespace: item.dedicatedNmsp },
      onConfirmHandler: onCancelDedicatedHandler
    });
    setConfirmModal(true);
  };

  const onCancelDedicatedHandler = async (data: Node) => {
    const response = await cancelDedicatedNode(data);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: '경고',
        content: '노드 지정 해제 실패',
        okBtn: '확인'
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };
  // @ts-ignore
  return (
    <>
      <div className="card min-w-full">
        <div className="card-header flex-wrap gap-5 py-5">
          <div className="flex gap-6">
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="switch switch-sm">
                <button className="btn btn-sm btn-light" onClick={() => onSearch(nodeRequestData.startNum)}>
                  <i className="ki-filled ki-arrows-circle"></i>
                </button>
              </label>
              <label className="form-label w-16">공급자</label>
              {/* <div className="flex grow flex-col items-start gap-5">
                <div className="relative w-full">
                  <input
                    className="input input-sm"
                    name="owner"
                    placeholder="E-Mail"
                    type="text"
                    value={nodeRequestData?.owner}
                    onChange={onInputChange}
                  />
                </div>
              </div> */}
              <div className="dropdown" data-dropdown="true" id="email_dropdown" data-dropdown-trigger="click" data-dropdown-dismiss="true">
                <div className="dropdown-toggle flex grow flex-col items-start gap-5">
                  <div className="relative w-full">
                    <input
                      className="input input-sm"
                      name="owner"
                      placeholder="E-Mail"
                      type="text"
                      value={nodeRequestData?.owner}
                      onChange={onInputChange}
                      onKeyDown={onInputEnter}
                    />
                  </div>
                </div>
                <div className="dropdown-content w-full max-w-[400px] p-4">
                  <div className="menu menu-default scrollable-y flex max-h-[400px] w-full flex-col">
                    {emailSearchList.length > 0 ? (
                      <>
                        <div className="menu-item" key={`email_search_key_a`}>
                          <span className="menu-title">검색 : {emailSearchList.length} 개</span>
                        </div>

                        <div className="menu-separator"></div>

                        {emailSearchList.map((item, index) => (
                          <div className="menu-item" key={`email_search_key_${index}`}>
                            <button className="menu-link" onClick={() => selectUser('owner', item.email)}>
                              <span className="menu-icon">
                                <i className="ki-outline ki-badge"></i>
                              </span>
                              <span className="menu-title">{item.email}</span>
                            </button>
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="menu-item" key={`email_search_key_a`}>
                        {nodeRequestData?.owner.length > 0 ? (
                          <span className="menu-title">검색된 이메일이 없습니다.</span>
                        ) : (
                          <span className="menu-title">이메일을 입력하세요.</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">노드이름</label>
              {/* <div className="flex grow flex-col items-start gap-5">
                <div className="relative w-full">
                  <input
                    className="input input-sm w-24"
                    name="name"
                    placeholder="wn"
                    type="text"
                    value={nodeRequestData?.name}
                    onChange={onInputChange}
                  />
                </div>
              </div> */}

              <div className="dropdown" data-dropdown="true" id="node_dropdown" data-dropdown-trigger="click" data-dropdown-dismiss="true">
                <div className="dropdown-toggle flex grow flex-col items-start gap-5">
                  <div className="relative w-full">
                    <input
                      className="input input-sm"
                      name="name"
                      placeholder="wn"
                      type="text"
                      value={nodeRequestData?.name}
                      onChange={onInputChange}
                      onKeyDown={onInputEnter}
                    />
                  </div>
                </div>
                <div className="dropdown-content w-full max-w-[400px] p-4">
                  <div className="menu menu-default scrollable-y flex max-h-[400px] w-full flex-col">
                    {nodeSearchList.length > 0 ? (
                      <>
                        <div className="menu-item" key={`node_search_key_a`}>
                          <span className="menu-title">검색 : {nodeSearchList.length} 개</span>
                        </div>

                        <div className="menu-separator"></div>

                        {nodeSearchList.map((item, index) => (
                          <div className="menu-item" key={`node_search_key_${index}`}>
                            <button className="menu-link" onClick={() => selectUser('name', item)}>
                              <span className="menu-icon">
                                <i className="ki-outline ki-badge"></i>
                              </span>
                              <span className="menu-title">{item}</span>
                            </button>
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="menu-item" key={`node_search_key_a`}>
                        {nodeRequestData?.name.length > 0 ? (
                          <span className="menu-title">검색된 노드가 없습니다.</span>
                        ) : (
                          <span className="menu-title">노드이름을 입력하세요.</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">유형</label>
              <div className="flex grow flex-col items-start gap-5">
                <div className="relative w-32">
                  <select className="select w-28" name="category" value={nodeRequestData?.category} onChange={onSelectChange}>
                    <option value="">선택</option>
                    <option value="csp">Tier1</option>
                    <option value="svr">Tier2</option>
                    <option value="pc">Tier3</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">상태</label>
              <div className="flex grow flex-col items-start gap-5">
                <div className="relative w-32">
                  <select className="select w-28" name="state" value={nodeRequestData?.state} onChange={onSelectChange}>
                    <option value="">선택</option>
                    <option value="open">미개통</option>
                    <option value="provis">개통</option>
                    <option value="run">실행</option>
                    <option value="fail">실패</option>
                    <option value="stop">중지</option>
                    <option value="create">생성</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
              <label className="form-label w-16">지정노드</label>
              <div className="flex grow flex-col items-start gap-5">
                <div className="relative w-32">
                  <select className="select w-28" name="isDedicated" value={nodeRequestData?.isDedicated} onChange={onSelectChange}>
                    <option value="">전체</option>
                    <option value="true">지정</option>
                    <option value="false">미지정</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-6">
            <div className="flex flex-wrap items-center gap-2.5">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <label className="form-label w-16">등록날짜</label>
                <div className="flex flex-col flex-wrap gap-2.5">
                  <DatePicker
                    name="startTime"
                    onChange={(value) => changeDataTime('startTime', value)}
                    value={isBlank(nodeRequestData?.startTime) ? null : dayjs(nodeRequestData?.startTime)}
                    className="input-sm max-w-40 !pl-0 !pr-0"
                    format="YYYY-MM-DD"
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </div>
                <div className="flex hidden flex-wrap items-center gap-2.5 lg:block lg:flex-col lg:justify-center"> - </div>
                <div className="flex flex-col flex-wrap gap-2.5">
                  <DatePicker
                    name="endTime"
                    onChange={(value) => changeDataTime('endTime', value)}
                    value={isBlank(nodeRequestData?.endTime) ? null : dayjs(nodeRequestData?.endTime)}
                    className="input-sm max-w-40 !pl-0 !pr-0"
                    format="YYYY-MM-DD"
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </div>
              </LocalizationProvider>
            </div>
            <label className="switch switch-sm">
              <button className="btn btn-sm btn-light" onClick={() => onSearch(0)}>
                검색
              </button>
            </label>
          </div>
        </div>
        <div className="card-table scrollable-x-auto">
          <div className="scrollable-auto">
            <table className="d-node-table table align-middle text-2xs text-gray-600">
              <thead>
                <tr className="bg-gray-100">
                  <th className="min-w-20">
                    <span className="d-sort" data-sort-column="owner">
                      <span className="d-sort-label">공급자</span>
                      <span className="d-sort-icon"></span>
                    </span>
                  </th>
                  <th className="min-w-20">지정 사용자</th>
                  <th className="min-w-20">
                    <span className="d-sort" data-sort-column="name">
                      <span className="d-sort-label">이름</span>
                      <span className="d-sort-icon"></span>
                    </span>
                  </th>
                  <th className="min-w-20">
                    <span className="d-sort" data-sort-column="category">
                      <span className="d-sort-label">유형</span>
                      <span className="d-sort-icon"></span>
                    </span>
                  </th>
                  <th className="min-w-20">스펙</th>
                  <th className="min-w-20">MAC ID</th>
                  <th className="min-w-20">네트워크속도(MB)</th>
                  <th className="min-w-20">
                    <span className="d-sort" data-sort-column="state">
                      <span className="d-sort-label">상태</span>
                      <span className="d-sort-icon"></span>
                    </span>
                  </th>
                  <th className="min-w-20">워크로드</th>
                  <th className="min-w-20">기본요금</th>
                  <th className="min-w-20">이용요금</th>
                  <th className="min-w-16">
                    <span className="d-sort" data-sort-column="created_at">
                      <span className="d-sort-label">등록일시</span>
                      <span className="d-sort-icon"></span>
                    </span>
                  </th>
                  <th className="min-w-16">
                    <span className="d-sort" data-sort-column="last_seen">
                      <span className="d-sort-label">마지막 업데이트일시</span>
                      <span className="d-sort-icon"></span>
                    </span>
                  </th>
                  <th className="min-w-16"></th>
                </tr>
              </thead>
              <tbody>
                {nodesData.length > 0 &&
                  nodesData.map((item, index) => (
                    <tr key={`node_${index}`}>
                      <td className="">
                        <Link
                          className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active"
                          href={`/admin/account/user/info/${item.owner}`}
                        >
                          {emailId(item.owner)}
                        </Link>
                      </td>
                      <td className="text-center">
                        {isBlank(item.dedicatedNmsp) ? (
                          <i className="ki-solid ki-user"></i>
                        ) : (
                          <Link
                            className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active"
                            href={`/admin/account/user/info/${item.dedicatedNmsp}`}
                          >
                            {item.dedicatedNmsp}
                          </Link>
                        )}
                      </td>
                      <td>
                        <div className="flex grow items-center gap-2.5">
                          <Link
                            className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active"
                            href={`/admin/node/detail/${item.name}`}
                          >
                            {item.name}
                          </Link>
                        </div>
                      </td>
                      <td className="">{tier(item.category, item.cloud)}</td>
                      {isBlank(item.gpuSpec) && item.state === 'run' ? (
                        <td className="text-danger">그래픽 드라이브 인식 실패</td>
                      ) : (
                        <td className="">{spec(item.gpuSpec, item.hostSpec)}</td>
                      )}
                      <td className="">{item.macId}</td>
                      {/* <td className="">{item.lastUpSpeed.toFixed(1)}(AVG {item.avgUpSpeed.toFixed(1)})</td> */}
                      <td className="">{`${item.lastUpSpeed.toFixed(1)}(AVG ${item.avgUpSpeed.toFixed(1)})`}</td>
                      <td className="">
                        <div className="flex items-center gap-1">
                          {state(item.state)}
                          {item.taintStateArray.length > 0 && <NodeTaintState node={item} index={index}></NodeTaintState>}
                        </div>
                      </td>
                      <td>{item.podCount}</td>
                      <td className="">{comma(Number(item.unitPrice.toFixed(2)))}원</td>
                      <td className="">{comma(Number(item.usagePrice.toFixed(2)))}원</td>
                      <td className="">{dateTime(item.createdAt)}</td>
                      <td className="">{dateTime(item.lastSeen)}</td>
                      <td className="">
                        <div className="flex-inline menu" data-menu="true">
                          <div
                            className="menu-item menu-item-dropdown"
                            data-menu-item-offset="0, 10px"
                            data-menu-item-placement="bottom-end"
                            data-menu-item-toggle="dropdown"
                            data-menu-item-trigger="click|lg:click"
                          >
                            <button className="menu-toggle btn-icon btn btn-sm btn-clear btn-light">
                              <i className="ki-filled ki-dots-vertical"></i>
                            </button>
                            <div className="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                              <div className="menu-item">
                                <Link className="menu-link" href={`/admin/node/detail/${item.name}`}>
                                  <span className="menu-icon">
                                    <i className="ki-filled ki-notepad"></i>
                                  </span>
                                  <span className="menu-title">상세보기</span>
                                </Link>
                              </div>
                              <div className="menu-item">
                                {/* {isBlank(item.gpuSpec) ? (
                                  <Link className="menu-link" href="#">
                                    <span className="menu-icon text-lg text-gray-300">₩</span>
                                    <span className="menu-title"> 
                                      <span className="text-gray-300">가격설정</span>
                                    </span>
                                  </Link>  
                                ) : (
                                  <Link className="menu-link" href={`/admin/node/price/${item.name}`}>
                                    <span className="menu-icon text-lg">₩</span>
                                    <span className="menu-title">가격설정</span>
                                  </Link>
                                )} */}
                              </div>
                              <div className="menu-item">
                                <Link className="menu-link" href={`/admin/node/monitoring/${item.name}`}>
                                  <span className="menu-icon">
                                    <i className="ki-filled ki-graph-4"></i>
                                  </span>
                                  <span className="menu-title">모니터링</span>
                                </Link>
                              </div>
                              <div className="menu-item">
                                <Link className="menu-link" href={`/admin/node/income/history/${item.name}`}>
                                  <span className="menu-icon">
                                    <i className="ki-filled ki-arrow-up-refraction"></i>
                                  </span>
                                  <span className="menu-title">수입내역</span>
                                </Link>
                              </div>
                              {item.category === 'csp' && item.cloud === 'ncp' ? (
                                <div className="menu-item">
                                  <button className="menu-link" onClick={() => openCloudModal(index)}>
                                    <span className="menu-icon">
                                      <i className="ki-filled ki-abstract"></i>
                                    </span>
                                    <span className="menu-title">VM 파라미터</span>
                                  </button>
                                </div>
                              ) : (
                                ''
                              )}
                              {!isBlank(item.dedicatedNmsp) ? (
                                <div className="menu-item">
                                  <button className="menu-link" onClick={() => cancelDedicated(index)}>
                                    <span className="menu-icon">
                                      <i className="ki-filled ki-fasten"></i>
                                    </span>
                                    <span className="menu-title">지정해제</span>
                                  </button>
                                </div>
                              ) : (
                                <div className="menu-item">
                                  <button className="menu-link" onClick={() => openDedicatedModal(index)}>
                                    <span className="menu-icon">
                                      <i className="ki-filled ki-fasten"></i>
                                    </span>
                                    <span className="menu-title">노드지정</span>
                                  </button>
                                </div>
                              )}
                              <div className="menu-item">
                                <button className="menu-link" onClick={() => openConfirmModal(index)}>
                                  <span className="menu-icon">
                                    <i className="ki-filled ki-trash"></i>
                                  </span>
                                  <span className="menu-title">삭제</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                {nodesData.length == 0 && (
                  <tr>
                    <td colSpan={11} className="text-center">
                      <div className="flex flex-col items-center p-10">
                        <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                        조회된 노드가 없습니다.
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
        <div className="card-footer flex-col justify-center gap-5 text-2sm font-medium text-gray-600 md:flex-row md:justify-between">
          <Paginate data={paging} requestData={nodeRequestData}></Paginate>
        </div>
      </div>
      {confirmModal && (
        <ConfirmModal
          confirmModalData={confirmModalData}
          confirmModal={confirmModal}
          onCloseModal={() => {
            setConfirmModal(false);
          }}
        ></ConfirmModal>
      )}
      {cloudModal && (
        <CloudModal
          cloudModal={cloudModal}
          cloudModalData={cloudModalData}
          onCloseModal={() => {
            setCloudModal(false);
          }}
        ></CloudModal>
      )}
      {dedicatedModal && (
        <DedicatedModal
          dedicatedModal={dedicatedModal}
          dedicatedModalData={dedicatedModalData}
          onCloseModal={() => {
            setCloudModal(false);
          }}
        ></DedicatedModal>
      )}
      {alertModal && (
        <AlertModal
          alertModal={alertModal}
          alertModalData={alertModalData}
          onCloseModal={() => {
            setAlertModal(false);
          }}
        ></AlertModal>
      )}
    </>
  );
}
