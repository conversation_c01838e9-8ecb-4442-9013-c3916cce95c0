/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-tooltip
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},i(t,e)};var o=e.utils.classSet,n=function(t){function e(e){var i=t.call(this,e)||this;return i.messages=new Map,i.opts=Object.assign({},{placement:"top",trigger:"click"},e),i.iconPlacedHandler=i.onIconPlaced.bind(i),i.validatorValidatedHandler=i.onValidatorValidated.bind(i),i.elementValidatedHandler=i.onElementValidated.bind(i),i.documentClickHandler=i.onDocumentClicked.bind(i),i}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}(e,t),e.prototype.install=function(){var t;this.tip=document.createElement("div"),o(this.tip,((t={"fv-plugins-tooltip":!0})["fv-plugins-tooltip--".concat(this.opts.placement)]=!0,t)),document.body.appendChild(this.tip),this.core.on("plugins.icon.placed",this.iconPlacedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.addEventListener("click",this.documentClickHandler)},e.prototype.uninstall=function(){this.messages.clear(),document.body.removeChild(this.tip),this.core.off("plugins.icon.placed",this.iconPlacedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.removeEventListener("click",this.documentClickHandler)},e.prototype.onIconPlaced=function(t){var e=this;if(o(t.iconElement,{"fv-plugins-tooltip-icon":!0}),"hover"===this.opts.trigger)t.iconElement.addEventListener("mouseenter",(function(i){return e.show(t.element,i)})),t.iconElement.addEventListener("mouseleave",(function(t){return e.hide()}));else t.iconElement.addEventListener("click",(function(i){return e.show(t.element,i)}))},e.prototype.onValidatorValidated=function(t){if(!t.result.valid){var e=t.elements,i=t.element.getAttribute("type"),o="radio"===i||"checkbox"===i?e[0]:t.element,n="string"==typeof t.result.message?t.result.message:t.result.message[this.core.getLocale()];this.messages.set(o,n)}},e.prototype.onElementValidated=function(t){if(t.valid){var e=t.elements,i=t.element.getAttribute("type"),o="radio"===i||"checkbox"===i?e[0]:t.element;this.messages.delete(o)}},e.prototype.onDocumentClicked=function(t){this.hide()},e.prototype.show=function(t,e){if(this.isEnabled&&(e.preventDefault(),e.stopPropagation(),this.messages.has(t))){o(this.tip,{"fv-plugins-tooltip--hide":!1}),this.tip.innerHTML='<div class="fv-plugins-tooltip__content">'.concat(this.messages.get(t),"</div>");var i=e.target.getBoundingClientRect(),n=this.tip.getBoundingClientRect(),l=n.height,a=n.width,s=0,r=0;switch(this.opts.placement){case"bottom":s=i.top+i.height,r=i.left+i.width/2-a/2;break;case"bottom-left":s=i.top+i.height,r=i.left;break;case"bottom-right":s=i.top+i.height,r=i.left+i.width-a;break;case"left":s=i.top+i.height/2-l/2,r=i.left-a;break;case"right":s=i.top+i.height/2-l/2,r=i.left+i.width;break;case"top-left":s=i.top-l,r=i.left;break;case"top-right":s=i.top-l,r=i.left+i.width-a;break;default:s=i.top-l,r=i.left+i.width/2-a/2}s+=window.scrollY||document.documentElement.scrollTop||document.body.scrollTop||0,r+=window.scrollX||document.documentElement.scrollLeft||document.body.scrollLeft||0,this.tip.setAttribute("style","top: ".concat(s,"px; left: ").concat(r,"px"))}},e.prototype.hide=function(){this.isEnabled&&o(this.tip,{"fv-plugins-tooltip--hide":!0})},e}(e.Plugin);t.Tooltip=n}));
