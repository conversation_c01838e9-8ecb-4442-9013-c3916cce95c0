/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-declarative
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Declarative=t(e.FormValidation))}(this,(function(e){"use strict";var t=function(e,a){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])},t(e,a)};return function(e){function a(t){var a=e.call(this,t)||this;return a.addedFields=new Map,a.opts=Object.assign({},{html5Input:!1,pluginPrefix:"data-fvp-",prefix:"data-fv-"},t),a.fieldAddedHandler=a.onFieldAdded.bind(a),a.fieldRemovedHandler=a.onFieldRemoved.bind(a),a}return function(e,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function n(){this.constructor=e}t(e,a),e.prototype=null===a?Object.create(a):(n.prototype=a.prototype,new n)}(a,e),a.prototype.install=function(){var e=this;this.parsePlugins();var t=this.parseOptions();Object.keys(t).forEach((function(a){e.addedFields.has(a)||e.addedFields.set(a,!0),e.core.addField(a,t[a])})),this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},a.prototype.uninstall=function(){this.addedFields.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},a.prototype.onFieldAdded=function(e){var t=this,a=e.elements;a&&0!==a.length&&!this.addedFields.has(e.field)&&(this.addedFields.set(e.field,!0),a.forEach((function(a){var n=t.parseElement(a);if(!t.isEmptyOption(n)){var i={selector:e.options.selector,validators:Object.assign({},e.options.validators||{},n.validators)};t.core.setFieldOptions(e.field,i)}})))},a.prototype.onFieldRemoved=function(e){e.field&&this.addedFields.has(e.field)&&this.addedFields.delete(e.field)},a.prototype.parseOptions=function(){var e=this,t=this.opts.prefix,a={},n=this.core.getFields(),i=this.core.getFormElement();return[].slice.call(i.querySelectorAll("[name], [".concat(t,"field]"))).forEach((function(n){var i=e.parseElement(n);if(!e.isEmptyOption(i)){var r=n.getAttribute("name")||n.getAttribute("".concat(t,"field"));a[r]=Object.assign({},a[r],i)}})),Object.keys(a).forEach((function(e){Object.keys(a[e].validators).forEach((function(t){a[e].validators[t].enabled=a[e].validators[t].enabled||!1,n[e]&&n[e].validators&&n[e].validators[t]&&Object.assign(a[e].validators[t],n[e].validators[t])}))})),Object.assign({},n,a)},a.prototype.createPluginInstance=function(e,t){for(var a=e.split("."),n=window||this,i=0,r=a.length;i<r;i++)n=n[a[i]];if("function"!=typeof n)throw new Error("the plugin ".concat(e," doesn't exist"));return new n(t)},a.prototype.parsePlugins=function(){for(var e,t=this,a=this.core.getFormElement(),n=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),i=a.attributes.length,r={},o=0;o<i;o++){var s=a.attributes[o].name,l=a.attributes[o].value,d=n.exec(s);if(d&&4===d.length){var c=this.toCamelCase(d[1]);r[c]=Object.assign({},d[3]?((e={})[this.toCamelCase(d[3])]=l,e):{enabled:""===l||"true"===l},r[c])}}Object.keys(r).forEach((function(e){var a=r[e],n=a.enabled,i=a.class;if(n&&i){delete a.enabled,delete a.clazz;var o=t.createPluginInstance(i,a);t.core.registerPlugin(e,o)}}))},a.prototype.isEmptyOption=function(e){var t=e.validators;return 0===Object.keys(t).length&&t.constructor===Object},a.prototype.parseElement=function(e){for(var t=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),a=e.attributes.length,n={},i=e.getAttribute("type"),r=0;r<a;r++){var o=e.attributes[r].name,s=e.attributes[r].value;if(this.opts.html5Input)switch(!0){case"minlength"===o:n.stringLength=Object.assign({},{enabled:!0,min:parseInt(s,10)},n.stringLength);break;case"maxlength"===o:n.stringLength=Object.assign({},{enabled:!0,max:parseInt(s,10)},n.stringLength);break;case"pattern"===o:n.regexp=Object.assign({},{enabled:!0,regexp:s},n.regexp);break;case"required"===o:n.notEmpty=Object.assign({},{enabled:!0},n.notEmpty);break;case"type"===o&&"color"===s:n.color=Object.assign({},{enabled:!0,type:"hex"},n.color);break;case"type"===o&&"email"===s:n.emailAddress=Object.assign({},{enabled:!0},n.emailAddress);break;case"type"===o&&"url"===s:n.uri=Object.assign({},{enabled:!0},n.uri);break;case"type"===o&&"range"===s:n.between=Object.assign({},{enabled:!0,max:parseFloat(e.getAttribute("max")),min:parseFloat(e.getAttribute("min"))},n.between);break;case"min"===o&&"date"!==i&&"range"!==i:n.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(s)},n.greaterThan);break;case"max"===o&&"date"!==i&&"range"!==i:n.lessThan=Object.assign({},{enabled:!0,max:parseFloat(s)},n.lessThan)}var l=t.exec(o);if(l&&4===l.length){var d=this.toCamelCase(l[1]);n[d]||(n[d]={}),l[3]?n[d][this.toCamelCase(l[3])]=this.normalizeValue(s):!0===n[d].enabled&&!1===n[d].enabled||(n[d].enabled=""===s||"true"===s)}}return{validators:n}},a.prototype.normalizeValue=function(e){return"true"===e||""===e||"false"!==e&&e},a.prototype.toUpperCase=function(e){return e.charAt(1).toUpperCase()},a.prototype.toCamelCase=function(e){return e.replace(/-./g,this.toUpperCase)},a}(e.Plugin)}));
