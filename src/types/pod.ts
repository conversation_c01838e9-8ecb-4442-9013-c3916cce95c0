import { Paging } from './paging';

export interface Pod {
  podName: string;
  nodeName: string;
  hostIp: string;
  podIp: string;
  state: string;
}

export interface PodStateCount {
  count: number;
  state: string;
}

export interface PodEventResponse {
  status: number;
  events: PodEvent[];
  paging: Paging;
}

export interface PodEvent {
  count: number;
  firstTimestamp: string;
  kind: string;
  lastTimestamp: string;
  manager: string;
  message: string;
  name: string;
  namespace: string;
  operation: string;
  reason: string;
  reportingComponent: string;
  reportingInstance: string;
  resType: string;
  type: string;
  resourceVersion: string;
  nodeName: string;
}

export interface PodEventRequest {
  type: string | undefined;
  owner: string | undefined;
  podName: string | undefined;
  nodeName: string | undefined;
  startNum: number | undefined;
  scaleNum: number | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
}

export interface PodDeployEventRequest {
  owner: string | undefined;
  podName: string | undefined;
  nodeName: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
}

export interface PodStateCount {
  count: number;
  state: string;
}

export interface PodState {
  total: number;
  pending: number;
  running: number;
  succeeded: number;
  failed: number;
  unknown: number;
  terminating: number;
}
