'use server';

import { getHeaders } from '@/lib/fetch-header';
import { GpuPriceForm, GPUPriceOrder } from '@/types/pricing.ts';

/**
 * @brief 플랫폼에 설정된 GPU가격 목록
 * @param cloud
 * @param gpuName
 * @param productCode
 * @returns
 */
export const getGpuPricing = async (cloud: string, gpuName: string, productCode: string) => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/list?cloud=${cloud}&gpuName=${gpuName}&productCode=${productCode}`,
      {
        method: 'GET',
        headers: { 'Content-Type': `application/json;charset=UTF-8` }
      }
    );

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

/**
 * @brief 새 GPU 모델 등록
 * @param body
 * @returns
 */
export const registerGPUPricing = async (body: GpuPriceForm) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/register`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });
    if (response.ok) {
      return await response.json();
    } else {
      const responseError = await response.json();
      return { status: responseError.status, data: responseError };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief GPU 가격 정보 수정
 * @param body
 * @returns
 */
export const updateGPUPricing = async (body: GpuPriceForm) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/update`, {
      method: 'PUT',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      const responseError = await response.json();
      return { status: responseError.status, data: responseError };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief GPU 가격 정보 삭제
 * @param body
 * @returns
 */
export const deleteGPUPricing = async (ser: number) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/delete/${ser}`, {
      method: 'DELETE',
      headers: await getHeaders(),

      credentials: 'include'
    });
    console.log(ser);
    console.log(response);
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief GPU가격 이력 목록
 * @param cloud
 * @param gpuName
 * @param productCode
 * @returns
 */
export const getGpuPricingLogs = async (pricingSer: number) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/log/list?pricingSer=${pricingSer}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    console.log(response);
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

/**
 * @brief GPU 가격 정보 수정
 * @param body
 * @returns
 */
export const updateOrders = async (body: GPUPriceOrder) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/update/orders`, {
      method: 'PUT',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      const responseError = await response.json();
      return { status: responseError.status, data: responseError };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};