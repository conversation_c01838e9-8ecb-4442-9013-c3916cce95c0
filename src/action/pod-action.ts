'use server';
import { getHeaders } from '@/lib/fetch-header';
import { generateUrlSearchParams } from '@/utils';
/**
 * @brief pod 목록
 * @returns
 */
export const getPods = async (owner: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/list?owner=${owner}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getPodEvents = async (data: any) => {
  try {
    const queryStr = generateUrlSearchParams(data);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/event/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getPodDeployEvents = async (data: any) => {
  try {
    const queryStr = generateUrlSearchParams(data);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/deploy/events?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getPodsByWorkload = async (ser: string, namespace: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/list/workload/${ser}?namespace=${namespace}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 파드 상태별 카운트
 * @returns
 */
export const getPodStateCount = async (owner: string) => {
  let controller = new AbortController();
  let signal = controller.signal;
  const id = setTimeout(() => controller.abort(), 10000);
  try {
    const queryParams: any = {
      owner: owner
    };
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/state/count?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders(),
      signal: controller.signal
    });

    clearTimeout(id);

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    clearTimeout(id);
    console.log('error', error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief PodName 검색
 * @param params
 * @returns
 */
export const getSearchPodName = async (params: string) => {
  try {
    console.log(params);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/search/${params}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};
