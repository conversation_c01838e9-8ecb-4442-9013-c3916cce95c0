'use server';
import { generateUrlSearchParams } from '@/utils';
import { getHeaders } from '@/lib/fetch-header';

/**
 * @brief 사용자 목록
 * @param email
 * @returns
 */
export const getUsers = async (params: any) => {
  const queryStr = generateUrlSearchParams(params);
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};
/**
 * @brief 사용자 목록
 * @param email
 * @returns
 */
export const getExcelJsonUsers = async (params: any) => {
  const queryStr = generateUrlSearchParams(params);
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/excel/json?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};
/**
 * @brief 사용자 목록
 * @param email
 * @returns
 */
export const getUser = async (email: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/${email}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};

/**
 * @brief 사용자 정보 수정
 * @param body
 * @returns
 */
export const updateUser = async (body: any) => {
  const url = `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/update`;

  try {
    const response = await fetch(url, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};

/**
 * @brief 사용자 삭제
 * @param email
 * @returns
 */
export const delUser = async (email: string) => {
  const url = `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/delete/${email}`;

  try {
    const response = await fetch(url, {
      method: 'DELETE',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};
/**
 * @brief 사용자 그룹 설정
 * @param body
 * @returns
 */
export const setUserGroup = async (body: any) => {
  console.log(body);
  const url = `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/group/set`;
  try {
    const response = await fetch(url, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });
    console.log(response);
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};
/**
 * @brief 사용자 Credentail 목록
 * @param email
 * @returns
 */
export const getCredentails = async (email: string) => {
  try {
    const queryStr = generateUrlSearchParams({ owner: email });
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 Credentail 등록
 * @param body
 * @returns
 */
export const registerCredential = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/register`, {
      method: 'POST',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 Credentail 수정
 * @param body
 * @returns
 */
export const updateCredential = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/update`, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 Credentail 삭제
 * @param category
 * @param cloud
 * @returns
 */
export const deleteCredential = async (category: string, cloud: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/delete/${category}/${cloud}`, {
      method: 'DELETE',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 이름, 이메일, 네임스페이스 검색
 * @param params
 * @returns
 */
export const getSearchUser = async (type: string, keyword: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/search/${type}/${keyword}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};
