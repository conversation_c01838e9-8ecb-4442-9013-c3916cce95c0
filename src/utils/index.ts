import dayjs from '@/lib/dayjs-lib';

export const generateUrlSearchParams = (params: any) => {
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    // Only append parameters that have defined values
    if (value !== undefined && value !== null) {
      // queryParams.append(key, value == null ? '' : value.toString());
      queryParams.append(key, value.toString());
    }
  });

  return queryParams.toString();
};

export const convertYearMonth = (date: number[]) => {
  let now;

  if (date != null) {
    if (date.length === 6) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      // return dayjs(now).format('YYYY-MM-DD HH:mm:ss');
    } else {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
    }

    // return dayjs(now.getTime() + now.getTimezoneOffset() * 60000 * -1).format('YYYY-MM-DD HH:mm:ss');
    return dayjs(now).tz().format('YYYY-MM');
  } else {
    return '';
  }
};

export const convertDateTime = (date: Date) => {
  return dayjs(date).tz().format('YYYY-MM-DD HH:mm:ss');
};

export const convertDate = (date: Date, time?: string) => {
  return dayjs(date).tz().format('YYYY-MM-DD') + ' ' + time;
};

export const utcTime = (date: number[]) => {
  let now;

  if (date != null) {
    if (date.length === 6) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      // return dayjs(now).format('YYYY-MM-DD HH:mm:ss');
    } else {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
    }

    return dayjs(now.getTime() + now.getTimezoneOffset() * 60000 * -1).format('YYYY-MM-DD HH:mm:ss');
    // return dayjs(now.getTime()).format('YYYY-MM-DD HH:mm:ss');
  } else {
    return '';
  }
};

export const dateTime = (date: number[]) => {
  let now;

  if (date != null) {
    if (date.length === 6 || date.length === 7) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      return dayjs(now).format('YYYY-MM-DD HH:mm:ss');
    } else if (date.length === 5) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
      return dayjs(now).format('YYYY-MM-DD HH:mm');
    } else if (date.length === 4) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3]));
      return dayjs(now).format('YYYY-MM-DD HH');
    } else if (date.length === 3) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2]));
      return dayjs(now).format('YYYY-MM-DD');
    } else {
      now = new Date(Date.UTC(date[0], date[1] - 1));
      return dayjs(now).format('YYYY-MM-DD');
    }
  } else {
    return '';
  }
};
export const dateDay = (date: number[]) => {
  if (date != null) {
    return dayjs(new Date(Date.UTC(date[0], date[1] - 1, date[2]))).format('YYYY-MM-DD');
  } else {
    return '';
  }
};
export const dateMonth = (date: number[]) => {
  if (date != null) {
    return dayjs(new Date(Date.UTC(date[0], date[1] - 1, date[2]))).format('YYYY-MM');
  } else {
    return '';
  }
};
export const dateTime2 = (date: string) => {
  let d = date.replace('Z', '').split('T');
  let dates = d[0].split('-');
  let times = d[1].split(':');
  if (times.length === 2) {
    times.push('00');
  }
  let now = new Date(
    Date.UTC(Number(dates[0]), Number(dates[1]) - 1, Number(dates[2]), Number(times[0]), Number(times[1]), Number(times[2]))
  );

  return dayjs(now.getTime()).format('YYYY-MM-DD HH:mm:ss');
};

export const mutcTime = (date: string) => {
  let d = date.split(' ');
  let dates = d[0].split('-');
  let times = d[1].split(':');
  let now = new Date(Number(dates[0]), Number(dates[1]) - 1, Number(dates[2]), Number(times[0]), Number(times[1]), Number(times[2]));
  let dd = new Date(now.getTime() + now.getTimezoneOffset() * 60000);
  return dayjs(dd).format('YYYY-MM-DD HH:mm:ss');
};

export const mutcTime2 = (date: number[]) => {
  let now;

  if (date != null) {
    if (date.length === 6) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      // return dayjs(now).format('YYYY-MM-DD HH:mm:ss');
    } else {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
    }

    return dayjs(now.getTime() + now.getTimezoneOffset() * 60000).format('YYYY-MM-DD HH:mm:ss');
  } else {
    return '';
  }
};

export const mutcDate = (date: number[]) => {
  let now;
  if (date != null) {
    now = new Date(Date.UTC(date[0], date[1] - 1, date[2]));
    return dayjs(now.getTime() + now.getTimezoneOffset() * 60000).format('YYYY-MM-DD');
  } else {
    return '';
  }
};

export const millToTime = (val: number) => {
  const seconds = Math.floor(val / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const remainingSeconds = seconds % 60;
  const remainingMinutes = minutes % 60;
  const reSecconds = remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds;
  const reMinutes = remainingMinutes < 10 ? '0' + remainingMinutes : remainingMinutes;
  const reHours = hours < 10 ? '0' + hours : hours;
  return reHours + ':' + reMinutes + ':' + reSecconds;
};

export const diffMinutes = (startTime: number[], endTime: number[]) => {
  const startDate = new Date(
    startTime[0],
    startTime[1] - 1,
    startTime[2],
    startTime[3],
    startTime[4],
    startTime[5] == undefined ? 0 : startTime[5]
  );
  const endDate = new Date(endTime[0], endTime[1] - 1, endTime[2], endTime[3], endTime[4], endTime[5] == undefined ? 0 : endTime[5]);
  const diffMs = endDate.getTime() - startDate.getTime();
  const diffMinutes = Math.floor(diffMs / 1000 / 60);
  return diffMinutes;
};

export const comma = (val: number) => {
  if (val == undefined || val == null) return '';
  // return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  const [integerPart, decimalPart] = val.toString().split('.'); // 정수부와 소수부 분리
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 정수부에 쉼표 추가
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger; // 소수부를 다시 붙임
};

export const convertGigabyte = (val: number) => {
  const gb = val / 1024;
  return parseFloat(gb.toFixed(2));
};

export const isMobileUserAgent = (userAgent: string) => {
  const mobileRegex = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  return mobileRegex.test(userAgent);
};

export const toBpsToByte = function (val: number) {
  const kb = val / 8;
  const mb = kb / 1024;
  const gb = mb / 1024;
  let res = { kb: parseFloat(kb.toFixed(2)), mb: parseFloat(mb.toFixed(2)), gb: parseFloat(gb.toFixed(2)), unit: '' };
  if (mb > 1024.0) {
    res.unit = 'GB';
  } else if (kb > 1024.0) {
    res.unit = 'MB';
  } else {
    res.unit = 'KB';
  }
  return res;
};

export const isBlank = (val: any) => {
  return typeof val == 'undefined' || val == null || val.trim() == '';
};

/**
 * @brief 전화번호 마스킹
 * @param item
 * @returns
 */
export const maskingPhone = (item: string) => {
  if (item == undefined || item == null) return '';
  let phone = item;
  if (item.indexOf('-') == -1) {
    phone = item.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
  }
  return phone.replace(/(\d{3})-(\d{3,4})-(\d{4})/, (match, p1, p2, p3) => {
    // 가운데 자리를 모두 *로 변환
    const maskedMiddle = p2.replace(/\d/g, '*');
    const maskedEnd = p3.replace(/(\d{2})(\d{2})/, '**$2');
    return `${p1}-${maskedMiddle}-${maskedEnd}`;
  });
};

/**
 * @brief 소숫점 자리수 표현
 * @param num
 * @param digit
 * @returns
 */
export const formatNumber = (num: number, digit: number) => {
  if (Number.isNaN(num)) return 0;
  return num % 1 === 0 ? num.toString() : num.toFixed(digit);
};

export const emailId = (email: string) => {
  return email.substring(0, email.indexOf('@'));
};
